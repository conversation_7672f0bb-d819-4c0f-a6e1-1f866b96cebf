pipeline {
    agent any

    environment {
        APP_NAME = 'sndp-agile-app'
        DOCKER_COMPOSE_FILE = 'docker-compose.yml'
        DEPLOYMENT_DIR = '/opt/sndp-agile'
        GITLAB_CREDENTIALS = 'gitlab-creds'
        DEPLOY_ENV = 'production'
    }
    
    options {
        buildDiscarder(logRotator(numToKeepStr: '10'))
        timeout(time: 30, unit: 'MINUTES')
        disableConcurrentBuilds()
    }
    
    stages {
        stage('🔍 Environment Check') {
            steps {
                script {
                    echo "DEBUG: Entering Environment Check stage"
                    echo "🚀 Starting deployment of ${APP_NAME}"
                    echo "📋 Build Number: ${BUILD_NUMBER}"
                    echo "🌿 Branch: ${env.BRANCH_NAME ?: 'main'}"
                    echo "🎯 Deploy Environment: ${DEPLOY_ENV}"
                    echo "🐳 Jenkins Container Mode: Using host Docker via socket"

                    sh '''
                        set -x
                        echo "Checking Docker access..."
                        docker --version || { echo "❌ Docker not accessible"; exit 1; }
                        echo "Detecting Docker Compose..."
                        if command -v docker-compose >/dev/null 2>&1; then
                            echo "✅ Found docker-compose (V1)"
                            docker-compose --version
                            echo "COMPOSE_CMD=docker-compose" > /tmp/compose_cmd
                        elif docker compose version >/dev/null 2>&1; then
                            echo "✅ Found docker compose (V2)"
                            docker compose version
                            echo "COMPOSE_CMD=docker compose" > /tmp/compose_cmd
                        else
                            echo "Installing Docker Compose V2..."
                            if command -v curl >/dev/null 2>&1; then
                                curl -L "https://github.com/docker/compose/releases/download/v2.24.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
                                chmod +x /usr/local/bin/docker-compose
                                ln -sf /usr/local/bin/docker-compose /usr/bin/docker-compose
                                echo "COMPOSE_CMD=docker-compose" > /tmp/compose_cmd
                            else
                                echo "❌ Cannot install Docker Compose"; exit 1
                            fi
                        fi

                        # Verify Docker Compose works
                        . /tmp/compose_cmd
                        $COMPOSE_CMD --version || { echo "❌ Docker Compose not working"; exit 1; }
                        echo "Testing Docker socket..."
                        docker info >/dev/null || { echo "❌ Cannot connect to Docker daemon"; exit 1; }
                        echo "Host Docker system info:"
                        docker system df
                        echo "Host system resources:"
                        docker run --rm alpine:latest sh -c "echo 'Memory info:' && cat /proc/meminfo | head -3; echo 'Disk info:' && df -h / | tail -1" 2>/dev/null || echo "Could not get system info"
                        echo "Checking host deployment directory..."
                        docker run --rm -v /opt:/opt alpine:latest ls -la /opt/ 2>/dev/null || echo "Host /opt directory not accessible"
                    '''
                }
            }
        }
        
        stage('📥 Clone Repository') {
            steps {
                script {
                    echo "DEBUG: Entering Clone Repository stage"
                    echo "📥 Cloning GitLab repository..."
                    cleanWs()
                    checkout([
                        $class: 'GitSCM',
                        branches: [[name: '*/main']],
                        userRemoteConfigs: [[
                            url: 'https://gitlab.com/g18853016/GS.git',
                            credentialsId: "${GITLAB_CREDENTIALS}"
                        ]]
                    ])
                    echo "✅ Repository cloned successfully"
                    sh '''
                        set -x
                        echo "Repository contents:"
                        ls -la
                        if [ ! -f "${DOCKER_COMPOSE_FILE}" ]; then
                            echo "❌ ${DOCKER_COMPOSE_FILE} not found in workspace"
                            exit 1
                        fi
                    '''
                }
            }
        }
        
        stage('🔧 Pre-deployment Setup') {
            steps {
                script {
                    echo "DEBUG: Entering Pre-deployment Setup stage"
                    echo "🔧 Setting up deployment environment..."

                    sh '''
                        set -x
                        echo "Creating deployment directory..."
                        docker run --rm -v /opt:/opt alpine:latest sh -c "mkdir -p ${DEPLOYMENT_DIR} && chown 1000:1000 ${DEPLOYMENT_DIR} && chmod 755 ${DEPLOYMENT_DIR}" || echo "Directory creation failed or already exists"
                        echo "Checking for existing containers..."
                        if docker ps --format "table {{.Names}}" | grep -E "(sndp|agile|web|db|ollama|prometheus|grafana)" >/dev/null 2>&1; then
                            echo "Stopping existing containers..."
                            docker ps -q --filter "name=web" | xargs -r docker stop || true
                            docker ps -q --filter "name=db" | xargs -r docker stop || true
                            docker ps -q --filter "name=ollama" | xargs -r docker stop || true
                            docker ps -q --filter "name=prometheus" | xargs -r docker stop || true
                            docker ps -q --filter "name=grafana" | xargs -r docker stop || true
                            cd ${DEPLOYMENT_DIR} && docker-compose -f ${DOCKER_COMPOSE_FILE} down 2>/dev/null || true
                            echo "Removing stopped containers..."
                            docker container prune -f || true
                        else
                            echo "No existing containers found"
                        fi
                        echo "Cleaning up old Docker images..."
                        docker image prune -f || true
                        echo "Current Docker containers:"
                        docker ps -a --format "table {{.Names}}\\t{{.Status}}\\t{{.Ports}}"
                    '''
                }
            }
        }
        
        stage('📦 Build Application') {
            steps {
                script {
                    echo "DEBUG: Entering Build Application stage"
                    echo "📦 Building Docker images..."

                    sh '''
                        set -x
                        echo "Verifying workspace files:"
                        ls -la
                        if [ ! -f "${DOCKER_COMPOSE_FILE}" ]; then
                            echo "❌ ${DOCKER_COMPOSE_FILE} not found in workspace"
                            exit 1
                        fi
                        echo "Copying files to host deployment directory..."
                        TEMP_CONTAINER=$(docker create --rm -v ${DEPLOYMENT_DIR}:/target alpine:latest)
                        docker cp . "${TEMP_CONTAINER}":/target/
                        docker start -a "${TEMP_CONTAINER}"
                        echo "Verifying files on host:"
                        docker run --rm -v ${DEPLOYMENT_DIR}:/app alpine:latest sh -c "ls -la /app/; if [ ! -f /app/${DOCKER_COMPOSE_FILE} ]; then echo '❌ ${DOCKER_COMPOSE_FILE} not found in target directory'; exit 1; fi; echo 'File copy verification passed'"
                        echo "Checking if rebuild is needed..."
                        # Check if containers are already running and healthy
                        RUNNING_CONTAINERS=$(docker ps --format "{{.Names}}" | grep -E "(web|db|ollama|prometheus|grafana)" | wc -l)

                        if [ "$RUNNING_CONTAINERS" -ge 4 ]; then
                            echo "✅ Most containers already running ($RUNNING_CONTAINERS/5). Skipping build for faster deployment."
                            echo "To force rebuild, stop containers first: docker-compose down"
                        else
                            echo "Building Docker images (using cache for faster builds)..."
                            . /tmp/compose_cmd
                            echo "Using compose command: $COMPOSE_CMD"
                            # Execute docker-compose on host using linuxserver/docker-compose
                            # Removed --no-cache for much faster builds
                            docker run --rm \
                                -v /var/run/docker.sock:/var/run/docker.sock \
                                -v ${DEPLOYMENT_DIR}:${DEPLOYMENT_DIR} \
                                -w ${DEPLOYMENT_DIR} \
                                --network host \
                                linuxserver/docker-compose:latest \
                                -f ${DOCKER_COMPOSE_FILE} build
                        fi
                        echo "Verifying built images..."
                        docker images --format "table {{.Repository}}\\t{{.Tag}}\\t{{.CreatedAt}}" | head -10
                    '''
                }
            }
        }
        
        stage('🧪 Health Check') {
            steps {
                script {
                    echo "DEBUG: Entering Health Check stage"
                    echo "🧪 Running pre-deployment health checks..."

                    sh '''
                        set -x
                        echo "Validating docker-compose.yml..."
                        # Run docker-compose config from a container that has access to the host files
                        docker run --rm \
                            -v ${DEPLOYMENT_DIR}:${DEPLOYMENT_DIR} \
                            -w ${DEPLOYMENT_DIR} \
                            linuxserver/docker-compose:latest \
                            -f ${DOCKER_COMPOSE_FILE} config >/dev/null
                        echo "Checking required files..."
                        docker run --rm -v ${DEPLOYMENT_DIR}:/app alpine:latest sh -c "test -f /app/${DOCKER_COMPOSE_FILE} || { echo '❌ ${DOCKER_COMPOSE_FILE} not found'; exit 1; }; test -f /app/Dockerfile || { echo '❌ Dockerfile not found'; exit 1; }; test -f /app/manage.py || { echo '❌ manage.py not found'; exit 1; }; echo '✅ All required files present'"
                    '''
                }
            }
        }

        stage('🚀 Deploy Application') {
            steps {
                script {
                    echo "DEBUG: Entering Deploy Application stage"
                    echo "🚀 Deploying application..."

                    sh '''
                        set -x
                        echo "Starting services..."
                        # Run docker-compose up from a container that has access to the host files
                        docker run --rm \
                            -v /var/run/docker.sock:/var/run/docker.sock \
                            -v ${DEPLOYMENT_DIR}:${DEPLOYMENT_DIR} \
                            -w ${DEPLOYMENT_DIR} \
                            --network host \
                            linuxserver/docker-compose:latest \
                            -f ${DOCKER_COMPOSE_FILE} up -d
                        echo "Waiting for services to start..."
                        sleep 15
                        echo "Checking container status..."
                        docker run --rm \
                            -v /var/run/docker.sock:/var/run/docker.sock \
                            -v ${DEPLOYMENT_DIR}:${DEPLOYMENT_DIR} \
                            -w ${DEPLOYMENT_DIR} \
                            linuxserver/docker-compose:latest \
                            -f ${DOCKER_COMPOSE_FILE} ps
                        echo "Recent container logs..."
                        docker run --rm \
                            -v /var/run/docker.sock:/var/run/docker.sock \
                            -v ${DEPLOYMENT_DIR}:${DEPLOYMENT_DIR} \
                            -w ${DEPLOYMENT_DIR} \
                            linuxserver/docker-compose:latest \
                            -f ${DOCKER_COMPOSE_FILE} logs --tail=50
                        echo "Checking for failed containers..."
                        docker ps -a --filter "status=exited" --format "table {{.Names}}\t{{.Status}}\t{{.Command}}"
                    '''
                }
            }
        }
        
        stage('✅ Deployment Verification') {
            steps {
                script {
                    echo "DEBUG: Entering Deployment Verification stage"
                    echo "✅ Verifying deployment..."

                    sh '''
                        set -x
                        echo "Checking if Django web container is running..."
                        WEB_CONTAINER=$(docker ps --format "{{.Names}}" | grep -E "(web|django|app)" | head -1)

                        if [ -z "$WEB_CONTAINER" ]; then
                            echo "❌ Django web container is not running!"
                            echo "Checking for failed containers..."
                            docker ps -a --filter "status=exited" --format "table {{.Names}}\t{{.Status}}\t{{.Command}}"
                            echo "Checking web container logs..."
                            WEB_CONTAINER_ALL=$(docker ps -a --format "{{.Names}}" | grep -E "(web|django|app)" | head -1)
                            if [ -n "$WEB_CONTAINER_ALL" ]; then
                                docker logs $WEB_CONTAINER_ALL --tail=30
                            else
                                echo "No web container found at all"
                            fi
                            exit 1
                        fi

                        echo "✅ Found Django container: $WEB_CONTAINER"
                   '''     
                }
            }
        }
    }
    
    post {
        always {
            script {
                echo "DEBUG: Entering post-always block"
                echo "🧹 Post-deployment cleanup..."

                sh '''
                    set -x
                    echo "Final deployment status:"
                    docker ps --format "table {{.Names}}\\t{{.Status}}\\t{{.Ports}}"
                    echo "Docker system usage:"
                    docker system df
                    echo "Deployment directory contents:"
                    docker run --rm -v ${DEPLOYMENT_DIR}:/app alpine:latest ls -la /app/ || echo "Cannot access deployment directory"
                '''
            }
        }

        success {
            script {
                echo "DEBUG: Entering post-success block"
                echo "🎉 Deployment completed successfully!"
                echo "Application should be accessible at:"
                echo "   - Django App: http://YOUR-HOST-IP:8000"
                echo "   - Grafana (if deployed): http://YOUR-HOST-IP:3000"
                echo "   - Prometheus (if deployed): http://YOUR-HOST-IP:9090"
                echo "Replace YOUR-HOST-IP with your actual server IP address"
            }
        }

        failure {
            script {
                echo "DEBUG: Entering post-failure block"
                echo "❌ Deployment failed!"

                sh '''
                    set -x
                    echo "Debugging information:"
                    docker ps -a --format "table {{.Names}}\\t{{.Status}}\\t{{.Ports}}"
                    echo "Recent logs:"
                    for container in $(docker ps -aq --filter "name=web" --filter "name=db"); do
                        if [ -n "$container" ]; then
                            echo "--- Logs for $container ---"
                            docker logs --tail=50 $container 2>&1 || echo "Could not get logs for $container"
                        fi
                    done
                    echo "Docker system info:"
                    docker system df
                    docker system events --since 5m --until now 2>/dev/null | tail -20 || true
                    echo "Deployment directory status:"
                    docker run --rm -v ${DEPLOYMENT_DIR}:/app alpine:latest ls -la /app/ || echo "Cannot access deployment directory"
                '''
            }
        }

        unstable {
            script {
                echo "DEBUG: Entering post-unstable block"
                echo "⚠️ Deployment completed with warnings"
                echo "🔍 Check the build logs for details"
            }
        }
    }
}