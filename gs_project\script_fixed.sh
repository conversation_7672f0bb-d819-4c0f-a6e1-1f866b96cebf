#!/bin/bash

# Exit on any error
set -e

# Check if running as root
if [ "$(id -u)" != "0" ]; then
  echo "This script must be run as root" 1>&2
  exit 1
fi

# System requirements check
echo "Checking system requirements..."
TOTAL_MEM=$(free -m | awk 'NR==2{printf "%.0f", $2}')
if [ "$TOTAL_MEM" -lt 4096 ]; then
  echo "Warning: SonarQube requires at least 4GB RAM. Current: ${TOTAL_MEM}MB"
  echo "Consider increasing VM memory or using lighter alternatives."
fi

# Set system limits for Elasticsearch (required for SonarQube)
echo "vm.max_map_count=262144" >> /etc/sysctl.conf
echo "fs.file-max=65536" >> /etc/sysctl.conf
sysctl -p

# Install Docker and Docker Compose if not present
echo "Installing Docker and Docker Compose..."
if ! command -v docker &> /dev/null; then
  apt-get update
  apt-get install -y docker.io curl
fi

# Install Docker Compose v2
if ! command -v docker-compose &> /dev/null; then
  curl -L "https://github.com/docker/compose/releases/download/v2.24.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
  chmod +x /usr/local/bin/docker-compose
fi

# Check Docker socket access
if [ ! -S /var/run/docker.sock ]; then
  echo "Docker socket not found at /var/run/docker.sock"
  exit 1
fi

# Enable and start Docker service
systemctl enable docker
systemctl start docker

# Add user to docker group
usermod -aG docker aziz1

# Configure DNS for Docker
mkdir -p /etc/docker
cat << EOF > /etc/docker/daemon.json
{
  "dns": ["8.8.8.8", "8.8.4.4"],
  "log-driver": "json-file",
  "log-opts": {
    "max-size": "10m",
    "max-file": "3"
  }
}
EOF

# Reload Docker configuration
systemctl restart docker
sleep 10

# Create directories for Docker Compose files
echo "Creating project directories..."
mkdir -p /home/<USER>/{sonarqube,prometheus,grafana,elk,jenkins}
chown -R aziz1:aziz1 /home/<USER>
chmod -R 755 /home/<USER>

# Fixed SonarQube docker-compose.yml with proper configuration
echo "Creating SonarQube configuration..."
cat << EOF > /home/<USER>/sonarqube/docker-compose.yml
version: '3.8'

services:
  postgres:
    image: postgres:15-alpine
    container_name: sonarqube_postgres
    environment:
      POSTGRES_USER: sonar
      POSTGRES_PASSWORD: sonar123
      POSTGRES_DB: sonarqube
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U sonar -d sonarqube"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s
    networks:
      - sonarqube_net
    restart: unless-stopped
    mem_limit: 512m
    cpus: "0.5"

  sonarqube:
    image: sonarqube:10.6.0-community
    container_name: sonarqube_server
    ports:
      - "9000:9000"
    environment:
      SONAR_JDBC_URL: *****************************************
      SONAR_JDBC_USERNAME: sonar
      SONAR_JDBC_PASSWORD: sonar123
      SONAR_ES_BOOTSTRAP_CHECKS_DISABLE: "true"
      SONAR_WEB_JAVAOPTS: "-Xmx1g -Xms512m"
      SONAR_CE_JAVAOPTS: "-Xmx1g -Xms512m"
      SONAR_SEARCH_JAVAOPTS: "-Xmx512m -Xms512m"
    volumes:
      - sonarqube_data:/opt/sonarqube/data
      - sonarqube_logs:/opt/sonarqube/logs
      - sonarqube_extensions:/opt/sonarqube/extensions
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - sonarqube_net
    restart: unless-stopped
    mem_limit: 2g
    cpus: "1.0"
    ulimits:
      nofile:
        soft: 65536
        hard: 65536

volumes:
  sonarqube_data:
  sonarqube_logs:
  sonarqube_extensions:
  postgres_data:

networks:
  sonarqube_net:
    driver: bridge
EOF

# Create SonarQube startup script
cat << EOF > /home/<USER>/sonarqube/start.sh
#!/bin/bash
echo "Starting SonarQube services..."
cd /home/<USER>/sonarqube
docker-compose down || true
docker-compose pull
docker-compose up -d

echo "Waiting for SonarQube to start..."
timeout=300
counter=0
while [ \$counter -lt \$timeout ]; do
    if curl -s http://localhost:9000/api/system/status | grep -q "UP"; then
        echo "SonarQube is ready!"
        echo "Access SonarQube at: http://localhost:9000"
        echo "Default credentials: admin/admin"
        break
    fi
    echo "Waiting for SonarQube... (\$counter/\$timeout seconds)"
    sleep 10
    counter=\$((counter + 10))
done

if [ \$counter -ge \$timeout ]; then
    echo "SonarQube failed to start within \$timeout seconds"
    docker-compose logs sonarqube
    exit 1
fi
EOF

chmod +x /home/<USER>/sonarqube/start.sh
chown aziz1:aziz1 /home/<USER>/sonarqube/start.sh

# Prometheus docker-compose.yml (optimized)
echo "Creating Prometheus configuration..."
cat << EOF > /home/<USER>/prometheus/docker-compose.yml
version: '3.8'

services:
  prometheus:
    image: prom/prometheus:v2.54.1
    container_name: prometheus_server
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=15d'
      - '--web.enable-lifecycle'
    restart: unless-stopped
    mem_limit: 512m
    cpus: "0.5"

volumes:
  prometheus_data:
EOF

# Prometheus configuration (updated for your Django app)
cat << EOF > /home/<USER>/prometheus/prometheus.yml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

scrape_configs:
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  - job_name: 'sndp-agile-app'
    static_configs:
      - targets: ['host.docker.internal:8000']
    metrics_path: '/metrics'
    scrape_interval: 30s

  - job_name: 'sonarqube'
    static_configs:
      - targets: ['host.docker.internal:9000']
    metrics_path: '/api/monitoring/metrics'
    scrape_interval: 60s
EOF

# Grafana docker-compose.yml (optimized)
echo "Creating Grafana configuration..."
cat << EOF > /home/<USER>/grafana/docker-compose.yml
version: '3.8'

services:
  grafana:
    image: grafana/grafana:11.2.0
    container_name: grafana_server
    ports:
      - "3000:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./grafana.ini:/etc/grafana/grafana.ini:ro
    environment:
      GF_SECURITY_ADMIN_USER: aziz
      GF_SECURITY_ADMIN_PASSWORD: aziz123
      GF_INSTALL_PLUGINS: grafana-piechart-panel
    restart: unless-stopped
    mem_limit: 512m
    cpus: "0.5"

volumes:
  grafana_data:
EOF

# Grafana configuration
cat << EOF > /home/<USER>/grafana/grafana.ini
[server]
http_port = 3000

[security]
admin_user = aziz
admin_password = aziz123

[users]
allow_sign_up = false

[analytics]
reporting_enabled = false
check_for_updates = false
EOF

# Set proper ownership
chown -R aziz1:aziz1 /home/<USER>
chmod -R 755 /home/<USER>

echo "Configuration files created successfully!"
echo ""
echo "To start SonarQube:"
echo "  sudo -u aziz1 /home/<USER>/sonarqube/start.sh"
echo ""
echo "To start other services:"
echo "  cd /home/<USER>/prometheus && docker-compose up -d"
echo "  cd /home/<USER>/grafana && docker-compose up -d"
echo ""
echo "Service URLs:"
echo "  SonarQube: http://localhost:9000 (admin/admin)"
echo "  Prometheus: http://localhost:9090"
echo "  Grafana: http://localhost:3000 (aziz/aziz123)"
