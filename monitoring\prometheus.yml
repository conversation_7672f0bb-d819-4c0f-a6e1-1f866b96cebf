global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  # Prometheus itself
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # SNDP Agile Django Application
  - job_name: 'sndp-agile-django'
    static_configs:
      - targets: ['web:8000']
    metrics_path: '/metrics'
    scrape_interval: 30s
    scrape_timeout: 10s

  # PostgreSQL Database (if you add postgres_exporter later)
  - job_name: 'postgresql'
    static_configs:
      - targets: ['db:5432']
    scrape_interval: 60s
    scrape_timeout: 30s

  # Ollama AI Service (if it exposes metrics)
  - job_name: 'ollama'
    static_configs:
      - targets: ['ollama:11434']
    scrape_interval: 60s
    scrape_timeout: 30s

  # Grafana
  - job_name: 'grafana'
    static_configs:
      - targets: ['grafana:3000']
    metrics_path: '/metrics'
    scrape_interval: 60s
