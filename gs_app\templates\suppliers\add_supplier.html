<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Ajouter un Fournisseur</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css">
    <style>
        /* Base Layout */
        html, body {
            height: 100%;
            margin: 0;
        }

        body {
            display: flex;
            flex-direction: column;
            min-height: 100vh;
            background-color: #f8f9fa;
            color: #212529;
            transition: all 0.3s ease;
        }

        .main-container {
            flex: 1 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            margin: 20px auto;
            max-width: 600px;
        }

        footer {
            flex-shrink: 0;
            background-color: #f8f9fa;
            color: #212529;
            padding: 1rem 0;
            text-align: center;
        }

        /* Navbar */
        .navbar {
            background-color: #343a40;
            padding: 0.5rem 1rem;
        }

        .navbar-brand, .nav-link {
            color: white !important;
        }

        .nav-link:hover {
            color: #7ab8ff !important;
        }

        /* Dark Mode Styles */
        body.dark-mode {
            background-color: #121212;
            color: #e0e0e0;
        }

        .dark-mode .main-container {
            background-color: #1e1e1e;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
        }

        .dark-mode .navbar {
            background-color: #1e1e1e !important;
        }

        .dark-mode footer {
            background-color: #1e1e1e;
            color: #e0e0e0;
        }

        .dark-mode h2,
        .dark-mode .form-label,
        .dark-mode .form-text,
        .dark-mode label,
        .dark-mode p {
            color: #e0e0e0 !important;
        }

        .dark-mode .form-control,
        .dark-mode .form-select,
        .dark-mode input,
        .dark-mode textarea,
        .dark-mode select {
            background-color: #2d2d2d !important;
            color: #e0e0e0 !important;
            border-color: #444 !important;
        }

        .dark-mode .form-control:focus,
        .dark-mode .form-select:focus {
            background-color: #3d3d3d !important;
            border-color: #007bff !important;
            color: #e0e0e0 !important;
        }

        .dark-mode .btn-primary {
            background-color: #007bff;
            border-color: #007bff;
        }

        .dark-mode .btn-primary:hover {
            background-color: #0056b3;
            border-color: #004085;
        }

        .dark-mode a {
            color: #7ab8ff;
        }

        .dark-mode .card {
            background-color: #2d2d2d !important;
            border-color: #444 !important;
        }

        /* Dark Mode Toggle */
        .dark-mode-toggle-container {
            display: flex;
            align-items: center;
            margin-left: 15px;
        }

        .dark-mode-toggle {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 24px;
        }

        .dark-mode-toggle input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: 0.4s;
            border-radius: 24px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 16px;
            width: 16px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: 0.4s;
            border-radius: 50%;
        }

        .dark-mode-toggle input:checked + .slider {
            background-color: #007bff;
        }

        .dark-mode-toggle input:checked + .slider:before {
            transform: translateX(26px);
        }

        .dark-mode-toggle-label {
            margin-left: 10px;
            color: white;
        }

        .dark-mode .dark-mode-toggle-label {
            color: #e0e0e0;
        }

        /* General Styling */
        h2 {
            color: #343a40;
            margin-bottom: 20px;
        }

        .card {
            transition: background-color 0.3s ease;
        }

        .btn-block {
            width: 100%;
        }
    </style>
</head>
<body>
    <!-- Navigation Bar -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="{% url 'home' %}">Home</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'order_list' %}">Commandes</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'product_list' %}">Produits</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'supplier_list' %}">Fournisseurs</a>
                    </li>
                </ul>
                <div class="dark-mode-toggle-container">
                    <label class="dark-mode-toggle">
                        <input type="checkbox" id="toggleSwitch">
                        <span class="slider"></span>
                    </label>
                    <span class="dark-mode-toggle-label">Mode Sombre</span>
                </div>
            </div>
        </div>
    </nav>

    <!-- Add Supplier Form -->
    <div class="container main-container">
        <div class="card">
            <div class="card-header">
                <h2>Ajouter un Fournisseur</h2>
            </div>
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}
                    {{ form.as_p }}
                    <button type="submit" class="btn btn-primary btn-block">Ajouter</button>
                </form>
                <a href="{% url 'supplier_list' %}" class="btn btn-link mt-3">Retour à la liste des fournisseurs</a>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer>
        <p>© 2025 Gestion des Commandes - Tous droits réservés.</p>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Dark Mode Toggle
        const html = document.documentElement;
        const toggleSwitch = document.getElementById('toggleSwitch');

        // Load dark mode preference
        if (localStorage.getItem('darkMode') === 'true') {
            html.classList.add('dark-mode');
            toggleSwitch.checked = true;
        }

        // Toggle dark mode
        toggleSwitch.addEventListener('change', () => {
            html.classList.toggle('dark-mode');
            localStorage.setItem('darkMode', html.classList.contains('dark-mode'));
        });
    </script>
</body>
</html>