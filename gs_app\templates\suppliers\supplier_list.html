{% load static %}
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Liste des Fournisseurs</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css">
    <link href="{% static 'css/suppliers.css' %}" rel="stylesheet">
</head>
<body>
    <!-- Navigation Bar -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand d-flex align-items-center" href="{% url 'home' %}">
                <span class="fw-bold text-primary">SNDP</span>
                <span class="ms-1">Agil</span>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav mx-auto">
                    <li class="nav-item mx-2">
                        <a class="nav-link" href="{% url 'product_list' %}">
                            <i class="bi bi-fuel-pump"></i> Produits
                        </a>
                    </li>
                    <li class="nav-item mx-2">
                        <a class="nav-link" href="{% url 'order_list' %}">
                            <i class="bi bi-cart3"></i> Commandes
                        </a>
                    </li>
                    <li class="nav-item mx-2">
                        <a class="nav-link active" href="{% url 'supplier_list' %}">
                            <i class="bi bi-building"></i> Fournisseurs
                        </a>
                    </li>
                </ul>
                <div class="dark-mode-toggle-container">
                    <label class="dark-mode-toggle">
                        <input type="checkbox" id="darkModeToggle">
                        <span class="slider"></span>
                    </label>
                    <span class="dark-mode-toggle-label">Mode Sombre</span>
                </div>
            </div>
        </div>
    </nav>

    <!-- Header Section -->
    <div class="header-section">
        <div class="container">
            <div class="header-content text-center">
                <h1>
                    <i class="bi bi-building me-2"></i>
                    Gestion des Fournisseurs
                </h1>
                <p>Gérez vos partenaires fournisseurs</p>
            </div>
        </div>
    </div>

    <!-- Supplier List -->
    <div class="container">
        <div class="supplier-list shadow mb-4">
            <div class="card-header bg-transparent border-0 d-flex justify-content-between align-items-center">
                <h2><i class="bi bi-building me-2"></i>Liste des Fournisseurs</h2>
                <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#addSupplierModal">
                    <i class="bi bi-plus-circle me-1"></i> Ajouter un fournisseur
                </button>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th>ID</th>
                                <th>Nom</th>
                                <th>Contact</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for supplier in suppliers %}
                                <tr>
                                    <td>{{ supplier.id }}</td>
                                    <td>{{ supplier.name }}</td>
                                    <td>{{ supplier.contact }}</td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#editSupplierModal{{ supplier.id }}">
                                                <i class="bi bi-pencil"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#deleteSupplierModal{{ supplier.id }}">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            {% empty %}
                                <tr>
                                    <td colspan="4" class="text-center py-3">Aucun fournisseur trouvé</td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Supplier Modal -->
    <div class="modal fade" id="addSupplierModal" tabindex="-1" aria-labelledby="addSupplierModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addSupplierModalLabel">Ajouter un Fournisseur</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="addSupplierForm" method="post" action="{% url 'add_supplier' %}">
                        {% csrf_token %}
                        <div class="mb-3">
                            <label for="id_name" class="form-label">Nom</label>
                            <input type="text" class="form-control" id="id_name" name="name" required>
                        </div>
                        <div class="mb-3">
                            <label for="id_contact" class="form-label">Contact (8 chiffres)</label>
                            <input type="text" class="form-control" id="id_contact" name="contact" required pattern="[0-9]{8}" title="Le numéro doit contenir exactement 8 chiffres">
                            <div class="form-text">Entrez un numéro de téléphone à 8 chiffres</div>
                        </div>
                        <div class="text-end">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                            <button type="submit" class="btn btn-primary">Ajouter</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Supplier Modals -->
    {% for supplier in suppliers %}
    <div class="modal fade" id="editSupplierModal{{ supplier.id }}" tabindex="-1" aria-labelledby="editSupplierModalLabel{{ supplier.id }}" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editSupplierModalLabel{{ supplier.id }}">Modifier le Fournisseur</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="editSupplierForm{{ supplier.id }}" method="post" action="{% url 'edit_supplier' supplier.id %}">
                        {% csrf_token %}
                        <div class="mb-3">
                            <label for="id_name{{ supplier.id }}" class="form-label">Nom</label>
                            <input type="text" class="form-control" id="id_name{{ supplier.id }}" name="name" value="{{ supplier.name }}" required>
                        </div>
                        <div class="mb-3">
                            <label for="id_contact{{ supplier.id }}" class="form-label">Contact (8 chiffres)</label>
                            <input type="text" class="form-control" id="id_contact{{ supplier.id }}" name="contact" value="{{ supplier.contact }}" required pattern="[0-9]{8}" title="Le numéro doit contenir exactement 8 chiffres">
                            <div class="form-text">Entrez un numéro de téléphone à 8 chiffres</div>
                        </div>
                        <div class="text-end">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                            <button type="submit" class="btn btn-primary">Enregistrer</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    {% endfor %}

    <!-- Delete Supplier Modals -->
    {% for supplier in suppliers %}
    <div class="modal fade" id="deleteSupplierModal{{ supplier.id }}" tabindex="-1" aria-labelledby="deleteSupplierModalLabel{{ supplier.id }}" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="deleteSupplierModalLabel{{ supplier.id }}">Confirmer la suppression</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>Êtes-vous sûr de vouloir supprimer le fournisseur <strong>{{ supplier.name }}</strong> ?</p>
                    <p class="text-danger"><i class="bi bi-exclamation-triangle-fill"></i> Cette action est irréversible.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <form action="{% url 'delete_supplier' supplier.id %}" method="post" style="display:inline;">
                        {% csrf_token %}
                        <button type="submit" class="btn btn-danger">Supprimer</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
    {% endfor %}

    <!-- Footer -->
    <footer class="mt-5">
        <div class="container">
            <p class="text-center">© 2025 SNDP Agil - Tous droits réservés.</p>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Dark Mode Toggle
        const html = document.documentElement;
        const toggleSwitch = document.getElementById('darkModeToggle');

        // Load dark mode preference
        if (localStorage.getItem('darkMode') === 'true') {
            html.classList.add('dark-mode');
            toggleSwitch.checked = true;

            // Ensure table headers get proper styling in dark mode
            document.querySelectorAll('.table-light').forEach(el => {
                el.classList.add('table-dark');
                el.classList.remove('table-light');
            });
        }

        // Toggle dark mode
        toggleSwitch.addEventListener('change', () => {
            html.classList.toggle('dark-mode');
            localStorage.setItem('darkMode', html.classList.contains('dark-mode'));

            // Toggle table header styling based on dark mode
            const tableHeaders = document.querySelectorAll('thead');
            if (html.classList.contains('dark-mode')) {
                tableHeaders.forEach(header => {
                    if (header.classList.contains('table-light')) {
                        header.classList.add('table-dark');
                        header.classList.remove('table-light');
                    }
                });
            } else {
                tableHeaders.forEach(header => {
                    if (header.classList.contains('table-dark')) {
                        header.classList.add('table-light');
                        header.classList.remove('table-dark');
                    }
                });
            }
        });

        // Form validation for contact number
        document.addEventListener('DOMContentLoaded', function() {
            // Validate add form
            const addForm = document.getElementById('addSupplierForm');
            if (addForm) {
                addForm.addEventListener('submit', validateForm);
            }

            // Validate edit forms
            document.querySelectorAll('form[id^="editSupplierForm"]').forEach(form => {
                form.addEventListener('submit', validateForm);
            });

            function validateForm(e) {
                const contactInput = this.querySelector('input[name="contact"]');
                const contactValue = contactInput.value.trim();

                // Check if it's exactly 8 digits
                if (!/^\d{8}$/.test(contactValue)) {
                    e.preventDefault();
                    alert('Le numéro de contact doit contenir exactement 8 chiffres.');
                    contactInput.focus();
                    return false;
                }

                return true;
            }
        });
    </script>
</body>
</html>
