<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modifier le Produit</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { padding: 20px; background-color: #f8f9fa; }
        .form-container { 
            max-width: 600px; 
            margin: 0 auto; 
            background: white; 
            padding: 25px; 
            border-radius: 8px; 
            box-shadow: 0 0 15px rgba(0,0,0,0.1);
        }
        .btn-container { margin-top: 20px; }

        /* Dark Mode Styles */
        .dark-mode body {
            background-color: #121212;
            color: #e0e0e0;
        }
        .dark-mode .form-container {
            background-color: #1e1e1e;
            box-shadow: 0 0 15px rgba(255, 255, 255, 0.1);
        }
        .dark-mode .btn-outline-secondary, .dark-mode .btn-primary {
            color: #90caf9;
            border-color: #444;
        }
        .dark-mode .btn-outline-secondary:hover, .dark-mode .btn-primary:hover {
            color: #ffffff;
            border-color: #666;
        }
    </style>
</head>
<body>

    <!-- Navbar with Dark Mode Toggle -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container mt-4">
            <a class="navbar-brand" href="{% url 'home' %}">Home</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item"><a class="nav-link" href="{% url 'order_list' %}">Commandes</a></li>
                    <li class="nav-item"><a class="nav-link" href="{% url 'product_list' %}">Produits</a></li>
                    <li class="nav-item"><a class="nav-link" href="{% url 'supplier_list' %}">Fournisseurs</a></li>
                </ul>
                <!-- Dark Mode Toggle -->
                <div class="form-check form-switch text-light">
                    <input class="form-check-input" type="checkbox" id="darkModeToggle">
                    <label class="form-check-label" for="darkModeToggle">Mode sombre</label>
                </div>
            </div>
        </div>
    </nav>

    <!-- Product Edit Form -->
    <div class="form-container">
        <h2 class="mb-4">Modifier {{ product.name }}</h2>
        
        <form method="post" id="productForm">
            {% csrf_token %}
            
            <div class="mb-3">
                {{ form.as_p }}
            </div>
            
            <!-- Hidden field for restock choice -->
            <input type="hidden" name="restock" id="restockField" value="no">
            
            <div class="btn-container d-flex justify-content-between">
                <a href="{% url 'product_list' %}" class="btn btn-outline-secondary">Retour</a>
                <button type="submit" class="btn btn-primary">Enregistrer</button>
            </div>
        </form>
    </div>

    <script>
        document.getElementById('productForm').addEventListener('submit', function(e) {
            const quantityInput = document.querySelector('input[name="quantity_in_stock"]');
            const currentQuantity = parseInt(quantityInput.value);
            const restockField = document.getElementById('restockField');
            
            if (currentQuantity < 100) {
                const confirmRestock = confirm('Quantité faible (' + currentQuantity + '). Voulez-vous réapprovisionner (+100)?');
                
                if (confirmRestock) {
                    restockField.value = 'yes';
                    quantityInput.value = currentQuantity + 100; // Update for user feedback
                } else {
                    restockField.value = 'no';
                }
            }
        });

        // Dark Mode Toggle Logic
        const toggle = document.getElementById('darkModeToggle');
        const html = document.documentElement;

        // Load preference
        if (localStorage.getItem('darkMode') === 'true') {
            html.classList.add('dark-mode');
            toggle.checked = true;
        }

        toggle.addEventListener('change', function () {
            html.classList.toggle('dark-mode');
            localStorage.setItem('darkMode', html.classList.contains('dark-mode'));
        });
    </script>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
