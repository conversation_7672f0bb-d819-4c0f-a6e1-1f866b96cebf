# Docker Setup with <PERSON><PERSON><PERSON> for SNDP Agile

This guide explains how to run your SNDP Agile application with containerized Ollama for AI functionality.

## 🐳 Services Overview

Your Docker setup now includes:

- **web**: Django application (port 8000)
- **db**: PostgreSQL database (port 5432)
- **ollama**: Ollama AI service (port 11434)
- **ollama-init**: Automatic Llama3 model setup

## 🚀 Quick Start

### 1. Build and Start All Services

```bash
# Build and start all containers
docker-compose up --build

# Or run in detached mode
docker-compose up --build -d
```

### 2. First Time Setup

The first time you run this, it will:
1. Start the Ollama service
2. Automatically download the Llama3 model (~4GB)
3. Set up your Django application
4. Initialize the database

**Note**: The initial setup may take 10-30 minutes depending on your internet speed (Llama3 model download).

### 3. Check Service Status

```bash
# Check if all services are running
docker-compose ps

# Check Ollama logs
docker-compose logs ollama

# Check Django app logs
docker-compose logs web
```

## 🔧 Configuration

### Environment Variables

The following environment variables are configured in `docker-compose.yml`:

- `OLLAMA_HOST=http://ollama:11434` - Points Django to the containerized Ollama
- `DATABASE_URL` - PostgreSQL connection string
- `DEBUG=True` - Development mode

### Ollama Model

By default, the setup uses the **Llama3** model. To change the model:

1. Edit `init-ollama.sh` and change `"llama3"` to your preferred model
2. Update `views.py` and change the model name in the API calls

## 📝 Usage

### Accessing the Application

- **Django App**: http://localhost:8000
- **Ollama API**: http://localhost:11434
- **PostgreSQL**: localhost:5432

### AI Features

Once everything is running, you can:

1. **Chat with AI**: Use the chat feature on the home page
2. **Upload Excel Analysis**: Upload Excel files for AI-powered stock analysis
3. **Ask AI**: Use the "Ask AI" page for general queries

## 🛠️ Troubleshooting

### Ollama Not Responding

```bash
# Check if Ollama container is running
docker-compose ps ollama

# Restart Ollama service
docker-compose restart ollama

# Check Ollama logs
docker-compose logs ollama
```

### Model Not Downloaded

```bash
# Check ollama-init logs
docker-compose logs ollama-init

# Manually pull the model
docker-compose exec ollama ollama pull llama3
```

### Django App Issues

```bash
# Check Django logs
docker-compose logs web

# Restart Django service
docker-compose restart web

# Run migrations manually
docker-compose exec web python manage.py migrate
```

## 🔄 Development Workflow

### Making Changes

1. **Code Changes**: Your code is mounted as a volume, so changes are reflected immediately
2. **Dependencies**: If you add new Python packages, rebuild the container:
   ```bash
   docker-compose up --build web
   ```

### Database Operations

```bash
# Run migrations
docker-compose exec web python manage.py migrate

# Create superuser
docker-compose exec web python manage.py createsuperuser

# Access Django shell
docker-compose exec web python manage.py shell
```

### Stopping Services

```bash
# Stop all services
docker-compose down

# Stop and remove volumes (WARNING: This will delete your data)
docker-compose down -v
```

## 🎯 GPU Support (Optional)

If you have an NVIDIA GPU and want to use it with Ollama:

1. Install [NVIDIA Container Toolkit](https://docs.nvidia.com/datacenter/cloud-native/container-toolkit/install-guide.html)
2. Uncomment the GPU configuration in `docker-compose.yml`:
   ```yaml
   deploy:
     resources:
       reservations:
         devices:
           - driver: nvidia
             count: 1
             capabilities: [gpu]
   ```
3. Restart the services: `docker-compose up --build`

## 📊 Performance Notes

- **First Run**: Expect 10-30 minutes for initial setup (model download)
- **Subsequent Runs**: Services should start in 1-2 minutes
- **AI Responses**: May take 10-60 seconds depending on query complexity
- **Memory Usage**: Ollama with Llama3 requires ~8GB RAM minimum

## 🔐 Security Notes

For production deployment:
- Change default passwords in `docker-compose.yml`
- Set `DEBUG=False`
- Use environment files for sensitive data
- Configure proper firewall rules
- Use HTTPS with proper SSL certificates

## 📞 Support

If you encounter issues:
1. Check the logs: `docker-compose logs [service-name]`
2. Verify all services are running: `docker-compose ps`
3. Restart problematic services: `docker-compose restart [service-name]`
