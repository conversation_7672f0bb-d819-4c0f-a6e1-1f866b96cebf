from django.db import models


###  Modèle Catégorie
class Category(models.Model):
    name = models.CharField(max_length=100, unique=True)

    def __str__(self):
        return self.name

    # Relation One-to-Many avec Product
    def get_products(self):
        return self.products.all()

###  Modèle Fournisseur
class Supplier(models.Model):
    name = models.CharField(max_length=100)
    contact = models.CharField(max_length=100, blank=True, null=True)

    def __str__(self):
        return self.name

    # Relation One-to-Many avec Product
    def get_products(self):
        return self.products.all()

### Modèle Produit
class Product(models.Model):
    name = models.CharField(max_length=100)
    
    # Relation avec Category (One-to-Many)
    category = models.ForeignKey(Category, on_delete=models.CASCADE, related_name='products')
    
    # Relation avec Supplier (One-to-Many)
    supplier = models.ForeignKey(Supplier, on_delete=models.SET_NULL, null=True, blank=True, related_name='products')
    
    quantity_in_stock = models.PositiveIntegerField(default=0)
    price = models.DecimalField(max_digits=10, decimal_places=2)

    def __str__(self):
        return f"{self.name} ({self.category.name})"

    # Relation avec OrderDetail (Many-to-Many via OrderDetail)
    def get_orders(self):
        return self.order_details.all()

###  Modèle Commande
class Order(models.Model):
    date = models.DateTimeField(auto_now_add=True)
    status = models.CharField(max_length=50, choices=[('pending', 'En attente'), ('completed', 'Terminée')])

    def __str__(self):
        return f"Commande {self.id} - {self.status}"

    # Relation One-to-Many avec OrderDetail
    def get_order_details(self):
        return self.order_details.all()

    # Liste des produits commandés
    def get_products(self):
        return [detail.product for detail in self.order_details.all()]
    
    # Calcul du prix total de la commande
    def get_total_price(self):
        total = 0
        for detail in self.order_details.all():
            total += detail.get_total()
        return total

###  Modèle Détail de Commande (Relation Many-to-Many entre Order et Product)
class OrderDetail(models.Model):
    # Relation avec Order (One-to-Many)
    order = models.ForeignKey(Order, on_delete=models.CASCADE, related_name='order_details')
    
    # Relation avec Product (One-to-Many)
    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='order_details')

    quantity = models.PositiveIntegerField()
    price_at_order = models.DecimalField(max_digits=10, decimal_places=2)

    def __str__(self):
        return f"{self.quantity} x {self.product.name} (Commande {self.order.id})"
    
    def get_total(self):
        return self.quantity * self.price_at_order
