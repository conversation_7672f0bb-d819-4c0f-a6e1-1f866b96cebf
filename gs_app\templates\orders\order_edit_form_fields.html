<div class="mb-3">
    <label for="id_status" class="form-label">Statut</label>
    {{ form.status }}
    {% if order.status == 'pending' %}
    <div class="form-text text-info mt-1">
        <i class="bi bi-info-circle"></i> Changer le statut à "Terminée" mettra à jour les quantités de produits en stock.
    </div>
    {% endif %}
</div>

{% if order_details %}
<h5 class="mt-4 mb-3">Détails de la commande</h5>
<div class="table-responsive">
    <table class="table table-sm">
        <thead>
            <tr>
                <th>Produit</th>
                <th>Quantité</th>
                <th>Prix unitaire</th>
                <th>Total</th>
            </tr>
        </thead>
        <tbody>
            {% for detail in order_details %}
            <tr>
                <td>{{ detail.product.name }}</td>
                <td>{{ detail.quantity }}</td>
                <td>{{ detail.price_at_order }}€</td>
                <td>{{ detail.get_total }}€</td>
            </tr>
            {% endfor %}
        </tbody>
        <tfoot>
            <tr>
                <th colspan="3" class="text-end">Total de la commande:</th>
                <th>{{ order.get_total_price }}€</th>
            </tr>
        </tfoot>
    </table>
</div>
<div class="mt-3">
    <a href="{% url 'order_detail_list' order.id %}" class="btn btn-outline-info btn-sm" target="_blank">
        Gérer les détails de la commande
    </a>
</div>
{% else %}
<div class="alert alert-info mt-3">
    Cette commande ne contient aucun produit. 
    <a href="{% url 'order_detail_list' order.id %}" target="_blank">Ajouter des produits</a>
</div>
{% endif %}
