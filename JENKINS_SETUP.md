# Jenkins CI/CD Setup for SNDP Agile Application

This guide will help you set up <PERSON> to automatically deploy your SNDP Agile application from GitLab to your VM.

## 🎯 Prerequisites

- <PERSON> running in a container on the same VM
- Docker and Docker Compose available on the VM
- Private GitLab repository with your SNDP Agile project
- GitLab Personal Access Token

## 🔧 Jenkins Configuration Steps

### 1. **Install Required <PERSON> Plugins**

Go to **Manage Jenkins** → **Manage Plugins** → **Available** and install:

- ✅ **Git Plugin** (usually pre-installed)
- ✅ **Pipeline Plugin** (usually pre-installed)
- ✅ **Docker Pipeline Plugin**
- ✅ **GitLab Plugin**
- ✅ **Credentials Plugin** (usually pre-installed)

### 2. **Set Up GitLab Credentials**

1. Go to **Manage Jenkins** → **Manage Credentials**
2. Click on **(global)** domain
3. Click **Add Credentials**
4. Select **Username with password**:
   - **Username**: Your GitLab username
   - **Password**: Your GitLab Personal Access Token
   - **ID**: `gitlab-token` (must match Jenkins<PERSON>le)
   - **Description**: `GitLab Access Token`

### 3. **Create GitLab Personal Access Token**

1. Go to GitLab → **User Settings** → **Access Tokens**
2. Create token with scopes:
   - ✅ `read_repository`
   - ✅ `read_user`
3. Copy the token and use it as password in Jenkins credentials

### 4. **Update Jenkinsfile Repository URL**

Edit the `Jenkinsfile` and update line 65:
```groovy
url: 'https://gitlab.com/YOUR-USERNAME/YOUR-REPO-NAME.git'
```

Replace with your actual GitLab repository URL.

### 5. **Create Jenkins Pipeline Job**

1. **New Item** → **Pipeline** → Name: `sndp-agile-deployment`
2. **Pipeline Configuration**:
   - **Definition**: Pipeline script from SCM
   - **SCM**: Git
   - **Repository URL**: Your GitLab repo URL
   - **Credentials**: Select `gitlab-token`
   - **Branch**: `*/main` (or your default branch)
   - **Script Path**: `Jenkinsfile`

### 6. **Configure Jenkins Container Permissions**

If Jenkins is running in a container, ensure it can access Docker:

```bash
# Add jenkins user to docker group (on VM host)
sudo usermod -aG docker jenkins

# Or if using Jenkins container, mount Docker socket
# Add to your Jenkins docker-compose.yml:
volumes:
  - /var/run/docker.sock:/var/run/docker.sock
  - /usr/bin/docker:/usr/bin/docker
```

## 📋 Pipeline Stages Explained

### 🔍 **Environment Check**
- Verifies Docker and system resources
- Displays build information

### 📥 **Clone Repository**
- Cleans workspace
- Clones your private GitLab repository
- Uses your configured credentials

### 🔧 **Pre-deployment Setup**
- Creates deployment directory (`/opt/sndp-agile`)
- Stops existing containers
- Cleans up old Docker images

### 📦 **Build Application**
- Copies source code to deployment directory
- Builds Docker images using your Dockerfile
- Verifies images were created

### 🧪 **Health Check**
- Validates docker-compose.yml syntax
- Checks for required files
- Pre-deployment verification

### 🚀 **Deploy Application**
- Starts all services with `docker-compose up -d`
- Waits for services to initialize
- Shows container status and logs

### ✅ **Deployment Verification**
- Tests Django application response
- Verifies database connection
- Checks monitoring endpoints (Prometheus, Grafana)

## 🎯 Deployment Locations

- **Source Code**: `/opt/sndp-agile/`
- **Docker Compose**: Runs from deployment directory
- **Application**: http://localhost:8000
- **Grafana**: http://localhost:3000
- **Prometheus**: http://localhost:9090

## 🔧 Customization Options

### Environment Variables
Edit the `environment` section in Jenkinsfile:
```groovy
environment {
    APP_NAME = 'your-app-name'
    DEPLOYMENT_DIR = '/opt/your-app'
    GITLAB_CREDENTIALS = 'your-credential-id'
}
```

### Branch Configuration
Change the branch in the checkout stage:
```groovy
branches: [[name: '*/develop']] // or your branch
```

### Deployment Directory
Update `DEPLOYMENT_DIR` to your preferred location:
```groovy
DEPLOYMENT_DIR = '/home/<USER>/sndp-agile'
```

## 🚨 Troubleshooting

### **Permission Issues**
```bash
# Fix Jenkins permissions
sudo chown -R jenkins:jenkins /opt/sndp-agile
sudo chmod +x /opt/sndp-agile
```

### **Docker Socket Access**
```bash
# Check if Jenkins can access Docker
docker ps
# If fails, add Jenkins to docker group
sudo usermod -aG docker jenkins
```

### **GitLab Authentication**
- Verify Personal Access Token is valid
- Check token has correct permissions
- Ensure repository URL is correct

### **Container Startup Issues**
```bash
# Check logs manually
cd /opt/sndp-agile
docker-compose logs -f
```

## 📊 Monitoring Pipeline

### Build Triggers
- **Manual**: Click "Build Now"
- **Webhook**: Configure GitLab webhook for automatic builds
- **Scheduled**: Add cron trigger for regular deployments

### Build History
- Jenkins keeps last 10 builds
- View logs for each stage
- Compare deployment times

### Notifications
Add post-build actions for:
- Email notifications
- Slack integration
- GitLab status updates

## 🎉 Testing the Pipeline

1. **Trigger Build**: Click "Build Now" in Jenkins
2. **Monitor Progress**: Watch console output
3. **Verify Deployment**: Check application URLs
4. **Review Logs**: Check for any warnings or errors

## 🔄 Automatic Deployments

To enable automatic deployments on Git push:

1. **GitLab Webhook**:
   - Go to GitLab Project → Settings → Webhooks
   - URL: `http://your-jenkins-url/project/sndp-agile-deployment`
   - Trigger: Push events

2. **Jenkins Polling** (alternative):
   - In job configuration → Build Triggers
   - Check "Poll SCM"
   - Schedule: `H/5 * * * *` (every 5 minutes)

Your Jenkins pipeline is now ready to automatically deploy your SNDP Agile application! 🚀
