/* Dashboard Professional Design with Black & Yellow Theme */
:root {
    --primary-color: #000000;
    --secondary-color: #ffff00;
    --accent-color: #ffd700;
    --dark-color: #1f1f1f;
    --light-color: #f8fafc;
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --gradient-primary: linear-gradient(135deg, #000000 0%, #333333 100%);
    --gradient-secondary: linear-gradient(135deg, #ffff00 0%, #ffd700 100%);
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

/* Base Layout */
html, body {
    height: 100%;
    margin: 0;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', <PERSON><PERSON>, sans-serif;
}

body {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    background-color: var(--light-color);
    color: var(--text-primary);
    transition: all 0.3s ease;
}

.main-container {
    flex: 1;
    padding: 2rem 0;
}

/* Navigation */
.navbar {
    background: var(--gradient-primary) !important;
    backdrop-filter: blur(10px);
    box-shadow: var(--shadow-md);
    padding: 1rem 0;
    transition: all 0.3s ease;
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
    color: var(--secondary-color) !important;
    text-decoration: none;
}

.nav-link {
    color: rgba(255, 255, 255, 0.9) !important;
    font-weight: 500;
    padding: 0.5rem 1rem !important;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
    position: relative;
}

.nav-link:hover {
    color: var(--secondary-color) !important;
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateY(-1px);
}

/* Header Section */
.header-section {
    background: var(--gradient-primary);
    color: white;
    padding: 4rem 0 3rem;
    margin-bottom: 2rem;
    position: relative;
    overflow: hidden;
}

.header-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 100" fill="rgba(255,255,0,0.05)"><polygon points="1000,100 1000,0 0,100"/></svg>');
    background-size: cover;
}

.header-content {
    position: relative;
    z-index: 2;
}

.header-section h1 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    background: linear-gradient(45deg, #ffffff, var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.header-section p {
    font-size: 1rem;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 0;
}

/* Quick Actions */
.quick-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
    justify-content: center;
    margin-bottom: 2rem;
}

.quick-action-btn {
    background: white;
    border: 2px solid transparent;
    border-radius: 1rem;
    padding: 1.5rem 2rem;
    text-decoration: none;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-md);
    min-width: 200px;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.quick-action-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-secondary);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.quick-action-btn:hover::before {
    transform: scaleX(1);
}

.quick-action-btn:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
    border-color: var(--accent-color);
}

.quick-action-btn i {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    display: block;
}

.quick-action-btn span {
    font-weight: 600;
    font-size: 1rem;
}

/* Statistics Cards */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 3rem;
}

.stat-card {
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: var(--shadow-md);
    border: 1px solid rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-secondary);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
}

.stat-card:hover::before {
    transform: scaleX(1);
}

.stat-card .stat-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    display: block;
}

.stat-card .stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.stat-card .stat-label {
    color: var(--text-secondary);
    font-weight: 500;
    margin-bottom: 0;
}

/* Charts Section */
.charts-section {
    margin-bottom: 3rem;
}

.chart-container {
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: var(--shadow-md);
    border: 1px solid rgba(0, 0, 0, 0.05);
    margin-bottom: 2rem;
    position: relative;
    overflow: hidden;
}

.chart-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-secondary);
}

.chart-container h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 1.5rem;
    text-align: center;
}

/* Tables */
.table-container {
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: var(--shadow-md);
    border: 1px solid rgba(0, 0, 0, 0.05);
    margin-bottom: 2rem;
    position: relative;
    overflow: hidden;
}

.table-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-secondary);
}

.table-container h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 1.5rem;
}

.table {
    margin-bottom: 0;
}

.table th {
    background-color: var(--light-color);
    border: none;
    font-weight: 600;
    color: var(--text-primary);
    padding: 1rem;
}

.table td {
    border: none;
    padding: 1rem;
    vertical-align: middle;
}

.table tbody tr {
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    transition: background-color 0.2s ease;
}

.table tbody tr:hover {
    background-color: rgba(255, 255, 0, 0.05);
}

/* Buttons */
.btn-primary {
    background: var(--gradient-primary);
    border: none;
    color: var(--secondary-color);
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
    color: var(--secondary-color);
}

.btn-outline-primary {
    border-color: var(--primary-color);
    color: var(--primary-color);
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--secondary-color);
    transform: translateY(-1px);
}

/* Dark Mode Toggle */
.dark-mode-toggle-container {
    display: flex;
    align-items: center;
    margin-left: 15px;
}

.dark-mode-toggle {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.dark-mode-toggle input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: 0.4s;
    border-radius: 24px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: 0.4s;
    border-radius: 50%;
}

.dark-mode-toggle input:checked + .slider {
    background-color: var(--secondary-color);
}

.dark-mode-toggle input:checked + .slider:before {
    transform: translateX(26px);
}

.dark-mode-toggle-label {
    margin-left: 10px;
    color: white;
    font-weight: 500;
}

/* Footer */
footer {
    background: var(--gradient-primary);
    color: white;
    padding: 2rem 0;
    margin-top: auto;
}

footer p {
    margin: 0;
    color: rgba(255, 255, 255, 0.8);
    text-align: center;
}

/* Dark Mode Styles */
body.dark-mode {
    background-color: #121212;
    color: #e0e0e0;
}

.dark-mode .stat-card,
.dark-mode .chart-container,
.dark-mode .table-container,
.dark-mode .quick-action-btn {
    background-color: #1e1e1e;
    border-color: #444;
    color: #e0e0e0;
}

.dark-mode .stat-card .stat-number,
.dark-mode .chart-container h3,
.dark-mode .table-container h3 {
    color: #e0e0e0;
}

.dark-mode .table th {
    background-color: #2d2d2d;
    color: #e0e0e0;
}

.dark-mode .table tbody tr:hover {
    background-color: rgba(255, 255, 0, 0.1);
}

.dark-mode .dark-mode-toggle-label {
    color: #e0e0e0;
}

.dark-mode .btn-outline-primary {
    border-color: var(--secondary-color);
    color: var(--secondary-color);
}

.dark-mode .btn-outline-primary:hover {
    background-color: var(--secondary-color);
    color: var(--primary-color);
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-section h1 {
        font-size: 2rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .quick-actions {
        flex-direction: column;
        align-items: center;
    }

    .quick-action-btn {
        min-width: 250px;
    }
}
