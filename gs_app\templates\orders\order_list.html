{% load static %}
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Liste des Commandes</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css">
    <link href="{% static 'css/orders.css' %}" rel="stylesheet">
</head>
<body>
    <!-- Navigation Bar -->
    <nav class="navbar navbar-expand-lg shadow-sm">
        <div class="container">
            <a class="navbar-brand d-flex align-items-center" href="{% url 'home' %}">
                <span class="fw-bold text-primary">SNDP</span>
                <span class="ms-1">Agil</span>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav mx-auto">
                    <li class="nav-item mx-2">
                        <a class="nav-link" href="{% url 'product_list' %}">
                            <i class="bi bi-fuel-pump"></i> Produits
                        </a>
                    </li>
                    <li class="nav-item mx-2">
                        <a class="nav-link active" href="{% url 'order_list' %}">
                            <i class="bi bi-cart3"></i> Commandes
                        </a>
                    </li>
                    <li class="nav-item mx-2">
                        <a class="nav-link" href="{% url 'supplier_list' %}">
                            <i class="bi bi-building"></i> Fournisseurs
                        </a>
                    </li>
                </ul>
                <div class="dark-mode-toggle-container">
                    <label class="dark-mode-toggle">
                        <input type="checkbox" id="darkModeToggle">
                        <span class="slider"></span>
                    </label>
                    <span class="dark-mode-toggle-label">Mode Sombre</span>
                </div>
            </div>
        </div>
    </nav>

    <!-- Header Section -->
    <div class="header-section">
        <div class="container">
            <div class="header-content text-center">
                <h1>
                    <i class="bi bi-cart3 me-2"></i>
                    Gestion des Commandes
                </h1>
                <p>Gérez vos commandes de produits pétroliers</p>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="container main-container">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="order-list">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2 class="mb-0">Liste des Commandes</h2>
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addOrderModal">
                            <i class="bi bi-plus-circle"></i> Ajouter une Commande
                        </button>
                    </div>

                    {% if orders %}
                        {% for order in orders %}
                            <div class="order-item">
                                <div class="order-info">
                                    <strong>Commande #{{ order.id }}</strong>
                                    <span class="text-muted"> - {{ order.status }}</span>
                                    <div>Date: {{ order.date|date:"d/m/Y" }}</div>
                                </div>
                                <div class="order-actions">
                                    <button class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#editOrderModal{{ order.id }}">
                                        <i class="bi bi-pencil"></i> Modifier
                                    </button>
                                    <button class="btn btn-sm btn-outline-danger" data-bs-toggle="modal" data-bs-target="#deleteOrderModal{{ order.id }}">
                                        <i class="bi bi-trash"></i> Supprimer
                                    </button>
                                    <a href="{% url 'order_detail_list' order.id %}" class="btn btn-sm btn-outline-info">
                                        <i class="bi bi-list-ul"></i> Détails
                                    </a>
                                </div>
                            </div>

                            <!-- Edit Order Modal -->
                            <div class="modal fade" id="editOrderModal{{ order.id }}" tabindex="-1" aria-hidden="true">
                                <div class="modal-dialog">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h5 class="modal-title">Modifier la Commande #{{ order.id }}</h5>
                                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                        </div>
                                        <div class="modal-body">
                                            <form method="post" action="{% url 'edit_order' order.id %}" id="editOrderForm{{ order.id }}">
                                                {% csrf_token %}
                                                <div id="editFormContent{{ order.id }}">Chargement...</div>
                                            </form>
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                                            <button type="submit" form="editOrderForm{{ order.id }}" class="btn btn-primary">Enregistrer</button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Delete Order Modal -->
                            <div class="modal fade" id="deleteOrderModal{{ order.id }}" tabindex="-1" aria-hidden="true">
                                <div class="modal-dialog">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h5 class="modal-title">Confirmer la suppression</h5>
                                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                        </div>
                                        <div class="modal-body">
                                            <p>Êtes-vous sûr de vouloir supprimer la commande #{{ order.id }}?</p>
                                            <p class="text-danger">Cette action est irréversible.</p>
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                                            <form action="{% url 'delete_order' order.id %}" method="post">
                                                {% csrf_token %}
                                                <button type="submit" class="btn btn-danger">Supprimer</button>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                    {% else %}
                        <div class="alert alert-info">Aucune commande trouvée.</div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Add Order Modal -->
    <div class="modal fade" id="addOrderModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Ajouter une Commande</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form method="post" action="{% url 'add_order' %}" id="addOrderForm">
                        {% csrf_token %}
                        <div id="addOrderFormContent">Chargement...</div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="submit" form="addOrderForm" class="btn btn-primary">Ajouter</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer>
        <p>© 2025 Gestion des Commandes - Tous droits réservés.</p>
    </footer>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Dark Mode Toggle
        const html = document.documentElement;
        const toggleSwitch = document.getElementById('darkModeToggle');

        // Load dark mode preference
        if (localStorage.getItem('darkMode') === 'true') {
            html.classList.add('dark-mode');
            toggleSwitch.checked = true;
        }

        // Toggle dark mode
        toggleSwitch.addEventListener('change', () => {
            html.classList.toggle('dark-mode');
            localStorage.setItem('darkMode', html.classList.contains('dark-mode'));
        });

        // AJAX for loading form content
        document.addEventListener('DOMContentLoaded', function() {
            // Load add form content when modal is shown
            const addModal = document.getElementById('addOrderModal');
            if (addModal) {
                addModal.addEventListener('show.bs.modal', function() {
                    fetch('{% url "add_order" %}?format=form')
                        .then(response => {
                            if (!response.ok) throw new Error('Network response was not ok');
                            return response.text();
                        })
                        .then(html => {
                            const addFormContent = document.getElementById('addOrderFormContent');
                            if (addFormContent) {
                                addFormContent.innerHTML = html;
                                // Execute any scripts in the loaded content
                                const scripts = addFormContent.querySelectorAll('script');
                                scripts.forEach(script => {
                                    const newScript = document.createElement('script');
                                    newScript.textContent = script.textContent;
                                    addFormContent.appendChild(newScript);
                                    script.remove(); // Remove the original script
                                });
                            }
                        })
                        .catch(error => {
                            console.error('Error loading add form:', error);
                            const addFormContent = document.getElementById('addOrderFormContent');
                            if (addFormContent) {
                                addFormContent.innerHTML =
                                    '<div class="alert alert-danger">Erreur lors du chargement du formulaire. Veuillez vérifier votre connexion ou réessayer plus tard.</div>';
                            }
                        });
                });
            }

            // Load edit form content when modals are shown
            {% if orders %}
            const orderData = [
                {% for order in orders %}
                {
                    "id": "{{ order.id|escapejs }}",
                    "editUrl": "{% url 'edit_order' order.id %}?format=form"
                }{% if not forloop.last %},{% endif %}
                {% endfor %}
            ];

            // Process each order if orderData exists and is not empty
            if (orderData && orderData.length > 0) {
                orderData.forEach(function(order) {
                    const modalId = 'editOrderModal' + order.id;
                    const contentId = 'editFormContent' + order.id;
                    const formId = 'editOrderForm' + order.id;
                    const editModal = document.getElementById(modalId);

                    if (editModal) {
                        // Load form content when modal is shown
                        editModal.addEventListener('show.bs.modal', function() {
                            fetch(order.editUrl)
                                .then(response => {
                                    if (!response.ok) throw new Error('Network response was not ok');
                                    return response.text();
                                })
                                .then(html => {
                                    const contentElement = document.getElementById(contentId);
                                    if (contentElement) {
                                        contentElement.innerHTML = html;
                                        // Execute any scripts in the loaded content
                                        const scripts = contentElement.querySelectorAll('script');
                                        scripts.forEach(script => {
                                            const newScript = document.createElement('script');
                                            newScript.textContent = script.textContent;
                                            contentElement.appendChild(newScript);
                                            script.remove(); // Remove the original script
                                        });
                                    }
                                })
                                .catch(error => {
                                    console.error('Error loading edit form for order ' + order.id + ':', error);
                                    const contentElement = document.getElementById(contentId);
                                    if (contentElement) {
                                        contentElement.innerHTML =
                                            '<div class="alert alert-danger">Erreur lors du chargement du formulaire. Veuillez vérifier votre connexion ou réessayer plus tard.</div>';
                                    }
                                });
                        });

                        // Handle form submission
                        const form = document.getElementById(formId);
                        if (form) {
                            form.addEventListener('submit', function(e) {
                                e.preventDefault(); // Prevent default form submission

                                // Submit form via fetch API
                                fetch(form.action, {
                                    method: 'POST',
                                    body: new FormData(form),
                                    headers: {
                                        'X-Requested-With': 'XMLHttpRequest'
                                    }
                                })
                                .then(response => {
                                    if (!response.ok) throw new Error('Network response was not ok');
                                    // Close the modal and reload the page to show updated data
                                    const modal = bootstrap.Modal.getInstance(editModal);
                                    modal.hide();
                                    window.location.reload();
                                })
                                .catch(error => {
                                    console.error('Error submitting form:', error);
                                    alert('Erreur lors de la soumission du formulaire. Veuillez réessayer.');
                                });
                            });
                        }
                    }
                });
            }
            {% else %}
            // Handle case where no orders exist
            console.log('No orders available to process.');
            {% endif %}
        });
    </script>
</body>
</html>
```