version: '3.9'

services:
  web:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=**********************************/gs_mng_db
      - SECRET_KEY=django-insecure-vym%sk*1humbpjbh*_f=cb-+4@v59e+h7ahe^clcl3&tst3si+
      - DEBUG=True
      - EMAIL_HOST_USER=<EMAIL>
      - EMAIL_HOST_PASSWORD=phmcxtyakkmocbsq
      - OLLAMA_HOST=http://ollama:11434
    volumes:
      - .:/app
      - staticfiles:/app/staticfiles
      - media:/app/media
    depends_on:
      - db
      - ollama
    networks:
      - app-network

  db:
    image: postgres:15
    environment:
      - POSTGRES_DB=gs_mng_db
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=1234
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - app-network

  ollama:
    image: ollama/ollama:latest
    container_name: ollama
    ports:
      - "11434:11434"
    volumes:
      - ollama_data:/root/.ollama
    environment:
      - OLLAMA_ORIGINS=*
    networks:
      - app-network
    restart: unless-stopped
    # For GPU support (uncomment if you have NVIDIA GPU)
    # deploy:
    #   resources:
    #     reservations:
    #       devices:
    #         - driver: nvidia
    #           count: 1
    #           capabilities: [gpu]

  ollama-init:
    image: curlimages/curl:latest
    container_name: ollama-init
    depends_on:
      - ollama
    networks:
      - app-network
    volumes:
      - ./init-ollama.sh:/init-ollama.sh
    command: sh /init-ollama.sh
    restart: "no"

volumes:
  postgres_data:
  staticfiles:
  media:
  ollama_data:

networks:
  app-network:
    driver: bridge