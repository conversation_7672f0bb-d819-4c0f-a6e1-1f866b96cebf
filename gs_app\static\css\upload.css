/* Upload Stock Page Professional Design with Black & Yellow Theme */
:root {
    --primary-color: #000000;
    --secondary-color: #ffff00;
    --accent-color: #ffd700;
    --dark-color: #1f1f1f;
    --light-color: #f8fafc;
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --gradient-primary: linear-gradient(135deg, #000000 0%, #333333 100%);
    --gradient-secondary: linear-gradient(135deg, #ffff00 0%, #ffd700 100%);
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON> UI', <PERSON><PERSON>, sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--light-color);
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    transition: all 0.3s ease;
}

/* Navigation */
.navbar {
    background: var(--gradient-primary) !important;
    backdrop-filter: blur(10px);
    box-shadow: var(--shadow-md);
    padding: 1rem 0;
    transition: all 0.3s ease;
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
    color: var(--secondary-color) !important;
    text-decoration: none;
}

.nav-link {
    color: rgba(255, 255, 255, 0.9) !important;
    font-weight: 500;
    padding: 0.5rem 1rem !important;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
    position: relative;
}

.nav-link:hover {
    color: var(--secondary-color) !important;
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateY(-1px);
}

.nav-link.active {
    background-color: rgba(255, 255, 0, 0.2) !important;
    color: var(--secondary-color) !important;
}

/* Header Section */
.header-section {
    background: var(--gradient-primary);
    color: white;
    padding: 4rem 0 3rem;
    margin-bottom: 2rem;
    position: relative;
    overflow: hidden;
}

.header-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 100" fill="rgba(255,255,0,0.05)"><polygon points="1000,100 1000,0 0,100"/></svg>');
    background-size: cover;
}

.header-content {
    position: relative;
    z-index: 2;
}

.header-section h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    background: linear-gradient(45deg, #ffffff, var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.header-section p {
    font-size: 1.1rem;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 0;
}

/* Main Content */
.main-container {
    flex: 1 0 auto;
    background: white;
    border-radius: 1rem;
    padding: 3rem;
    box-shadow: var(--shadow-md);
    border: 1px solid rgba(0, 0, 0, 0.05);
    margin: 2rem auto;
    max-width: 800px;
    position: relative;
    overflow: hidden;
}

.main-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-secondary);
}

.main-container h1 {
    color: var(--text-primary);
    margin-bottom: 2rem;
    font-weight: 600;
    text-align: center;
}

/* Form Styling */
.form-group {
    margin-bottom: 2rem;
}

.form-group label {
    font-weight: 600;
    margin-bottom: 0.75rem;
    color: var(--text-primary);
    display: block;
}

.form-control {
    padding: 1rem;
    font-size: 1rem;
    line-height: 1.5;
    color: var(--text-primary);
    background-color: #fff;
    border: 2px solid #e2e8f0;
    border-radius: 0.75rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: var(--primary-color);
    outline: 0;
    box-shadow: 0 0 0 0.25rem rgba(0, 0, 0, 0.1);
}

/* Buttons */
.btn-primary {
    background: var(--gradient-primary);
    border: none;
    color: var(--secondary-color);
    font-weight: 600;
    padding: 1rem 2rem;
    border-radius: 0.75rem;
    transition: all 0.3s ease;
    font-size: 1.1rem;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    color: var(--secondary-color);
}

.w-100 {
    width: 100%;
}

/* Result Section */
.result {
    margin-top: 3rem;
    padding: 2rem;
    background-color: rgba(255, 255, 0, 0.05);
    border-radius: 1rem;
    border: 1px solid var(--accent-color);
}

.result h2 {
    color: var(--text-primary);
    margin-bottom: 1.5rem;
    font-weight: 600;
}

.result pre {
    background-color: #f8fafc;
    padding: 1.5rem;
    border-radius: 0.75rem;
    border: 1px solid #e2e8f0;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    line-height: 1.6;
    color: var(--text-primary);
    white-space: pre-wrap;
    word-wrap: break-word;
    transition: all 0.3s ease;
}

/* Dark Mode Toggle */
.dark-mode-toggle-container {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.dark-mode-toggle {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.dark-mode-toggle input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: 0.4s;
    border-radius: 24px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: 0.4s;
    border-radius: 50%;
}

.dark-mode-toggle input:checked + .slider {
    background-color: var(--secondary-color);
}

.dark-mode-toggle input:checked + .slider:before {
    transform: translateX(26px);
}

.dark-mode-toggle-label {
    color: white;
    font-weight: 500;
}

/* Footer */
footer {
    flex-shrink: 0;
    background: var(--gradient-primary);
    color: white;
    padding: 2rem 0;
    text-align: center;
}

footer p {
    margin: 0;
    color: rgba(255, 255, 255, 0.8);
}

/* Dark Mode Styles */
html.dark-mode,
html.dark-mode body {
    background-color: #121212 !important;
    color: #e0e0e0;
}

html.dark-mode .main-container {
    background-color: #1e1e1e !important;
    border-color: #444;
    color: #e0e0e0;
}

html.dark-mode .main-container h1 {
    color: #e0e0e0;
}

html.dark-mode .form-group label {
    color: #e0e0e0;
}

html.dark-mode .form-control {
    background-color: #2c2c2c;
    border-color: #444;
    color: #e0e0e0;
}

html.dark-mode .form-control:focus {
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 0.25rem rgba(255, 255, 0, 0.25);
}

html.dark-mode .result {
    background-color: rgba(255, 255, 0, 0.1);
    border-color: var(--accent-color);
}

html.dark-mode .result h2 {
    color: #e0e0e0;
}

html.dark-mode .result pre {
    background-color: #2c2c2c;
    border-color: #444;
    color: #e0e0e0;
}

html.dark-mode .dark-mode-toggle-label {
    color: #e0e0e0;
}

html.dark-mode footer {
    background: var(--gradient-primary);
    color: #e0e0e0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-section h1 {
        font-size: 2rem;
    }

    .main-container {
        margin: 1rem;
        padding: 2rem;
    }

    .btn-primary {
        padding: 0.875rem 1.5rem;
        font-size: 1rem;
    }
}
