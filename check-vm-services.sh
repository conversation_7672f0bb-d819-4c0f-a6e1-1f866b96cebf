#!/bin/bash

echo "🔍 Checking VM Services Status"
echo "=============================="

echo "1. Docker containers status:"
docker-compose ps

echo ""
echo "2. Port bindings:"
docker-compose ps --format "table {{.Name}}\t{{.Ports}}"

echo ""
echo "3. Testing local access inside VM:"
echo "   - Grafana: $(curl -s -o /dev/null -w "%{http_code}" http://localhost:3000 || echo "FAILED")"
echo "   - Prometheus: $(curl -s -o /dev/null -w "%{http_code}" http://localhost:9090 || echo "FAILED")"
echo "   - Django: $(curl -s -o /dev/null -w "%{http_code}" http://localhost:8000 || echo "FAILED")"

echo ""
echo "4. Network interfaces:"
ip addr show | grep -E "inet.*scope global"

echo ""
echo "5. Listening ports:"
netstat -tlnp | grep -E ":3000|:9090|:8000"

echo ""
echo "6. Firewall status (if ufw is installed):"
if command -v ufw >/dev/null 2>&1; then
    sudo ufw status
else
    echo "   ufw not installed"
fi

echo ""
echo "7. Docker network info:"
docker network ls
docker network inspect gs_project_app-network 2>/dev/null | grep -A 10 "Containers" || echo "Network not found"
