/* Modern Professional Design with Black & Yellow Theme */
:root {
    --primary-color: #000000;
    --secondary-color: #ffff00;
    --accent-color: #ffd700;
    --dark-color: #1f1f1f;
    --light-color: #f8fafc;
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --gradient-primary: linear-gradient(135deg, #000000 0%, #333333 100%);
    --gradient-secondary: linear-gradient(135deg, #ffff00 0%, #ffd700 100%);
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON> UI', <PERSON><PERSON>, sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--light-color);
    overflow-x: hidden;
}

/* Navigation */
.navbar {
    background: var(--primary-color) !important;
    backdrop-filter: blur(10px);
    box-shadow: var(--shadow-md);
    padding: 1rem 0;
    transition: all 0.3s ease;
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
    color: var(--secondary-color) !important;
    text-decoration: none;
}

.nav-link {
    color: rgba(255, 255, 255, 0.9) !important;
    font-weight: 500;
    padding: 0.5rem 1rem !important;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
    position: relative;
}

.nav-link:hover {
    color: var(--secondary-color) !important;
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateY(-1px);
}

/* Hero Section */
.hero-section {
    background: var(--gradient-primary);
    color: white;
    padding: 120px 0 80px;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 100" fill="rgba(255,255,0,0.05)"><polygon points="1000,100 1000,0 0,100"/></svg>');
    background-size: cover;
}

.hero-content {
    position: relative;
    z-index: 2;
}

.hero-section h1 {
    font-size: 3.5rem;
    font-weight: 800;
    margin-bottom: 1.5rem;
    background: linear-gradient(45deg, #ffffff, var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-section p {
    font-size: 1.25rem;
    margin-bottom: 2rem;
    color: rgba(255, 255, 255, 0.9);
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.hero-section img {
    max-width: 350px;
    height: auto;
    margin: 2rem 0;
    border-radius: 1rem;
    box-shadow: var(--shadow-xl);
    transition: transform 0.3s ease;
}

.hero-section img:hover {
    transform: scale(1.05);
}

.btn-hero {
    background: var(--gradient-secondary);
    border: none;
    color: var(--primary-color);
    font-weight: 700;
    padding: 1rem 2.5rem;
    border-radius: 50px;
    font-size: 1.1rem;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-lg);
}

.btn-hero:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
    color: var(--primary-color);
    background: linear-gradient(135deg, #ffd700 0%, #ffff00 100%);
}

/* Info Section */
.info-section {
    padding: 100px 0;
    background: linear-gradient(180deg, var(--light-color) 0%, #ffffff 100%);
}

.section-title {
    text-align: center;
    margin-bottom: 4rem;
}

.section-title h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.section-title p {
    font-size: 1.1rem;
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
}

/* Cards */
.feature-card {
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    height: 100%;
    box-shadow: var(--shadow-sm);
    border: 1px solid rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-secondary);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
}

.feature-card:hover::before {
    transform: scaleX(1);
}

.feature-card img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    border-radius: 0.75rem;
    margin-bottom: 1.5rem;
    transition: transform 0.3s ease;
}

.feature-card:hover img {
    transform: scale(1.05);
}

.feature-card h5 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.feature-card p {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: 0;
}

/* Modern Chat Interface Styles */
#chat-container {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 350px;
    z-index: 1000;
    box-shadow: var(--shadow-xl);
    border-radius: 1rem;
    overflow: hidden;
    font-family: 'Inter', sans-serif;
    background: white;
}

#chat-header {
    background: var(--gradient-primary);
    color: white;
    padding: 1rem 1.25rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
}

#chat-header h5 {
    margin: 0;
    font-size: 1rem;
    font-weight: 600;
    color: var(--secondary-color);
}

#toggle-chat {
    background: none;
    border: none;
    color: var(--secondary-color);
    font-size: 1.25rem;
    cursor: pointer;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.2s ease;
}

#toggle-chat:hover {
    background-color: rgba(255, 255, 0, 0.1);
}

#chat-messages {
    height: 300px;
    background: white;
    overflow-y: auto;
    padding: 1rem;
}

.message {
    margin-bottom: 1rem;
    max-width: 85%;
    word-wrap: break-word;
}

.user-message {
    margin-left: auto;
    text-align: right;
}

.user-message span {
    background: var(--gradient-primary);
    color: var(--secondary-color);
    padding: 0.75rem 1rem;
    border-radius: 1.25rem 1.25rem 0.25rem 1.25rem;
    display: inline-block;
    font-size: 0.9rem;
    box-shadow: var(--shadow-sm);
}

.bot-message {
    margin-right: auto;
    text-align: left;
}

.bot-message span {
    background: #f1f5f9;
    color: var(--text-primary);
    padding: 0.75rem 1rem;
    border-radius: 1.25rem 1.25rem 1.25rem 0.25rem;
    display: inline-block;
    font-size: 0.9rem;
    border: 1px solid #e2e8f0;
}

#chat-input-container {
    background: #f8fafc;
    padding: 1rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

#chat-input {
    flex: 1;
    padding: 0.75rem 1rem;
    border: 1px solid #e2e8f0;
    border-radius: 1.5rem;
    outline: none;
    font-size: 0.9rem;
    transition: border-color 0.2s ease;
}

#chat-input:focus {
    border-color: var(--primary-color);
}

#send-button {
    background: var(--gradient-primary);
    color: var(--secondary-color);
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: transform 0.2s ease;
    box-shadow: var(--shadow-sm);
}

#send-button:hover {
    transform: scale(1.05);
}

.typing-indicator {
    color: var(--text-secondary);
    font-style: italic;
    margin: 0.5rem 0;
    font-size: 0.85rem;
}

/* Footer */
.footer {
    background: var(--gradient-primary);
    color: white;
    padding: 4rem 0 2rem;
    margin-top: 5rem;
}

.footer h5 {
    font-weight: 600;
    margin-bottom: 1.5rem;
    color: var(--secondary-color);
}

.footer p, .footer a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer a:hover {
    color: var(--secondary-color);
}

.footer .btn-outline-light {
    border-color: var(--secondary-color);
    color: var(--secondary-color);
    transition: all 0.3s ease;
}

.footer .btn-outline-light:hover {
    background-color: var(--secondary-color);
    color: var(--primary-color);
    border-color: var(--secondary-color);
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-section h1 {
        font-size: 2.5rem;
    }
    
    .hero-section p {
        font-size: 1.1rem;
    }
    
    .section-title h2 {
        font-size: 2rem;
    }
    
    #chat-container {
        width: 300px;
        bottom: 10px;
        right: 10px;
    }
}
