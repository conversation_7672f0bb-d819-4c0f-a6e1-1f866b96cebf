{% load static %}
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Liste des Produits</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css">
    <link href="{% static 'css/products.css' %}" rel="stylesheet">
</head>
<body>
    <!-- Navigation Bar -->
    <nav class="navbar navbar-expand-lg shadow-sm">
        <div class="container">
            <a class="navbar-brand d-flex align-items-center" href="{% url 'home' %}">
                <span class="fw-bold text-primary">SNDP</span>
                <span class="ms-1">Agil</span>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav mx-auto">
                    <li class="nav-item mx-2">
                        <a class="nav-link {% if request.resolver_match.url_name == 'product_list' %}active{% endif %}" href="{% url 'product_list' %}">
                            <i class="bi bi-box me-1"></i>Produits
                        </a>
                    </li>
                    <li class="nav-item mx-2">
                        <a class="nav-link {% if request.resolver_match.url_name == 'order_list' %}active{% endif %}" href="{% url 'order_list' %}">
                            <i class="bi bi-cart me-1"></i>Commandes
                        </a>
                    </li>
                    <li class="nav-item mx-2">
                        <a class="nav-link {% if request.resolver_match.url_name == 'supplier_list' %}active{% endif %}" href="{% url 'supplier_list' %}">
                            <i class="bi bi-building me-1"></i>Fournisseurs
                        </a>
                    </li>
                </ul>
                <div class="dark-mode-toggle-container d-flex align-items-center">
                    <label class="dark-mode-toggle me-2">
                        <input type="checkbox" id="darkModeToggle">
                        <span class="slider"></span>
                    </label>
                    <span class="text-nowrap fs-sm">Mode Sombre</span>
                </div>
            </div>
        </div>
    </nav>

    <!-- Header Section -->
    <div class="header-section">
        <div class="container">
            <div class="header-content text-center">
                <h1>
                    <i class="bi bi-box-seam me-2"></i>
                    Gestion des Produits
                </h1>
                <p>Gérez votre inventaire de produits pétroliers</p>
            </div>
        </div>
    </div>

    <div class="container main-container">
        <div class="product-list">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h2>Liste des Produits</h2>
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addProductModal">
                    <i class="bi bi-plus-circle"></i> Ajouter un Produit
                </button>
            </div>

            <div class="list-group">
                {% for product in products %}
                <div class="product-item">
                    <div class="product-info">
                        <strong>{{ product.name }}</strong>
                        <span class="text-muted"> - {{ product.category }}</span>
                        <div>Prix: {{ product.price }}€ | Stock: {{ product.quantity_in_stock }}</div>
                    </div>
                    <div class="product-actions">
                        <button class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#editProductModal{{ product.id }}">
                            <i class="bi bi-pencil"></i> Modifier
                        </button>
                        <button class="btn btn-sm btn-outline-danger" data-bs-toggle="modal" data-bs-target="#deleteProductModal{{ product.id }}">
                            <i class="bi bi-trash"></i> Supprimer
                        </button>
                    </div>
                </div>

                <!-- Edit Product Modal -->
                <div class="modal fade" id="editProductModal{{ product.id }}" tabindex="-1" aria-hidden="true">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">Modifier {{ product.name }}</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                <form method="post" action="{% url 'edit_product' product.id %}" id="editProductForm{{ product.id }}">
                                    {% csrf_token %}
                                    <div id="editFormContent{{ product.id }}">Chargement...</div>
                                    <input type="hidden" name="restock" id="restockField{{ product.id }}" value="no">
                                </form>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                                <button type="submit" form="editProductForm{{ product.id }}" class="btn btn-primary">Enregistrer</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Delete Product Modal -->
                <div class="modal fade" id="deleteProductModal{{ product.id }}" tabindex="-1" aria-hidden="true">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">Confirmer la suppression</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                <p>Êtes-vous sûr de vouloir supprimer <strong>{{ product.name }}</strong>?</p>
                                <p class="text-danger">Cette action est irréversible.</p>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                                <form action="{% url 'delete_product' product.id %}" method="post">
                                    {% csrf_token %}
                                    <button type="submit" class="btn btn-danger">Supprimer</button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
                {% empty %}
                <div class="alert alert-info">Aucun produit trouvé.</div>
                {% endfor %}
            </div>
        </div>
    </div>

    <!-- Add Product Modal -->
    <div class="modal fade" id="addProductModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Ajouter un Produit</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form method="post" action="{% url 'add_product' %}" id="addProductForm">
                        {% csrf_token %}
                        <div id="addFormContent">Chargement...</div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="submit" form="addProductForm" class="btn btn-primary">Ajouter</button>
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Dark Mode Toggle and AJAX Script -->
    <script>
        // Dark Mode Toggle
        const toggle = document.getElementById('darkModeToggle');
        const html = document.documentElement;

        // Load preference
        if (localStorage.getItem('darkMode') === 'true') {
            html.classList.add('dark-mode');
            toggle.checked = true;
        }

        toggle.addEventListener('change', function() {
            html.classList.toggle('dark-mode');
            localStorage.setItem('darkMode', html.classList.contains('dark-mode'));
        });

        // AJAX for loading form content
        document.addEventListener('DOMContentLoaded', function() {
            // Load add form content when modal is shown
            const addModal = document.getElementById('addProductModal');
            if (addModal) {
                addModal.addEventListener('show.bs.modal', function() {
                    fetch('{% url "add_product" %}?format=form')
                        .then(response => {
                            if (!response.ok) throw new Error('Network response was not ok');
                            return response.text();
                        })
                        .then(html => {
                            document.getElementById('addFormContent').innerHTML = html;
                        })
                        .catch(error => {
                            console.error('Error loading add form:', error);
                            document.getElementById('addFormContent').innerHTML =
                                '<div class="alert alert-danger">Erreur lors du chargement du formulaire. Veuillez vérifier votre connexion ou réessayer plus tard.</div>';
                        });
                });
            }

            // Load edit form content when modals are shown
            const productData = [
            {% for product in products %}
                {
                    "id": "{{ product.id }}",
                    "editUrl": "{% url 'edit_product' product.id %}?format=form"
                }{% if not forloop.last %},{% endif %}
            {% endfor %}
            ];

            // Process each product
            productData.forEach(function(product) {
                const modalId = 'editProductModal' + product.id;
                const contentId = 'editFormContent' + product.id;
                const restockFieldId = 'restockField' + product.id;
                const editModal = document.getElementById(modalId);

                if (editModal) {
                    editModal.addEventListener('show.bs.modal', function() {
                        fetch(product.editUrl)
                            .then(response => {
                                if (!response.ok) throw new Error('Network response was not ok');
                                return response.text();
                            })
                            .then(html => {
                                document.getElementById(contentId).innerHTML = html;

                                // Add submit event listener to the form after it's loaded
                                const form = document.getElementById('editProductForm' + product.id);
                                if (form) {
                                    form.addEventListener('submit', function(e) {
                                        const quantityInput = form.querySelector('input[name="quantity_in_stock"]');
                                        if (quantityInput) {
                                            const currentQuantity = parseInt(quantityInput.value);
                                            const restockField = document.getElementById(restockFieldId);

                                            if (currentQuantity < 100 && restockField) {
                                                e.preventDefault(); // Prevent form submission
                                                const confirmRestock = confirm('Quantité faible (' + currentQuantity + '). Voulez-vous réapprovisionner (+100)?');

                                                if (confirmRestock) {
                                                    restockField.value = 'yes';
                                                    quantityInput.value = currentQuantity + 100; // Update for user feedback
                                                } else {
                                                    restockField.value = 'no';
                                                }

                                                // Continue with form submission
                                                form.submit();
                                            }
                                        }
                                    });
                                }
                            })
                            .catch(error => {
                                console.error('Error loading edit form for product ' + product.id + ':', error);
                                document.getElementById(contentId).innerHTML =
                                    '<div class="alert alert-danger">Erreur lors du chargement du formulaire. Veuillez vérifier votre connexion ou réessayer plus tard.</div>';
                            });
                    });
                }
            });
        });
    </script>
</body>
</html>
