#!/bin/bash

echo "🔧 Fixing Grafana Dashboard Issues in VM Environment"
echo "=================================================="

# Check if we're in the right directory
if [ ! -f "docker-compose.yml" ]; then
    echo "❌ Error: docker-compose.yml not found. Please run this script from your project root."
    exit 1
fi

# Create monitoring directories if they don't exist
echo "📁 Creating monitoring directories..."
mkdir -p monitoring/grafana/datasources
mkdir -p monitoring/grafana/dashboards

# Fix datasource configuration
echo "🔧 Fixing Prometheus datasource configuration..."
cat > monitoring/grafana/datasources/prometheus.yml << 'EOF'
apiVersion: 1

datasources:
  - name: Prometheus
    type: prometheus
    access: proxy
    url: http://prometheus:9090
    uid: prometheus
    isDefault: true
    editable: true
    jsonData:
      timeInterval: "5s"
EOF

# Create dashboard provider configuration
echo "🔧 Creating dashboard provider configuration..."
cat > monitoring/grafana/dashboards/dashboard.yml << 'EOF'
apiVersion: 1

providers:
  - name: 'default'
    orgId: 1
    folder: ''
    type: file
    disableDeletion: false
    updateIntervalSeconds: 10
    allowUiUpdates: true
    options:
      path: /etc/grafana/provisioning/dashboards
EOF

# Create a working Django dashboard
echo "📊 Creating working Django dashboard..."
cat > monitoring/grafana/dashboards/working-django-dashboard.json << 'EOF'
{
  "annotations": {
    "list": [
      {
        "builtIn": 1,
        "datasource": {
          "type": "datasource",
          "uid": "grafana"
        },
        "enable": true,
        "hide": true,
        "iconColor": "rgba(0, 211, 255, 1)",
        "name": "Annotations & Alerts",
        "type": "dashboard"
      }
    ]
  },
  "editable": true,
  "fiscalYearStartMonth": 0,
  "graphTooltip": 0,
  "id": null,
  "links": [],
  "liveNow": false,
  "panels": [
    {
      "datasource": {
        "type": "prometheus",
        "uid": "prometheus"
      },
      "fieldConfig": {
        "defaults": {
          "color": {
            "mode": "palette-classic"
          },
          "mappings": [],
          "thresholds": {
            "mode": "absolute",
            "steps": [
              {
                "color": "green",
                "value": null
              },
              {
                "color": "red",
                "value": 80
              }
            ]
          },
          "unit": "short"
        },
        "overrides": []
      },
      "gridPos": {
        "h": 4,
        "w": 6,
        "x": 0,
        "y": 0
      },
      "id": 1,
      "options": {
        "colorMode": "value",
        "graphMode": "area",
        "justifyMode": "center",
        "orientation": "auto",
        "reduceOptions": {
          "calcs": [
            "lastNotNull"
          ],
          "fields": "",
          "values": false
        },
        "textMode": "auto"
      },
      "pluginVersion": "10.0.0",
      "targets": [
        {
          "datasource": {
            "type": "prometheus",
            "uid": "prometheus"
          },
          "expr": "up{job=\"sndp-agile-django\"}",
          "format": "time_series",
          "interval": "",
          "legendFormat": "Django App Status",
          "refId": "A"
        }
      ],
      "title": "Django App Status",
      "type": "stat"
    },
    {
      "datasource": {
        "type": "prometheus",
        "uid": "prometheus"
      },
      "fieldConfig": {
        "defaults": {
          "color": {
            "mode": "palette-classic"
          },
          "mappings": [],
          "thresholds": {
            "mode": "absolute",
            "steps": [
              {
                "color": "green",
                "value": null
              },
              {
                "color": "red",
                "value": 80
              }
            ]
          },
          "unit": "short"
        },
        "overrides": []
      },
      "gridPos": {
        "h": 4,
        "w": 6,
        "x": 6,
        "y": 0
      },
      "id": 2,
      "options": {
        "colorMode": "value",
        "graphMode": "area",
        "justifyMode": "center",
        "orientation": "auto",
        "reduceOptions": {
          "calcs": [
            "lastNotNull"
          ],
          "fields": "",
          "values": false
        },
        "textMode": "auto"
      },
      "pluginVersion": "10.0.0",
      "targets": [
        {
          "datasource": {
            "type": "prometheus",
            "uid": "prometheus"
          },
          "expr": "up{job=\"prometheus\"}",
          "format": "time_series",
          "interval": "",
          "legendFormat": "Prometheus Status",
          "refId": "A"
        }
      ],
      "title": "Prometheus Status",
      "type": "stat"
    }
  ],
  "refresh": "5s",
  "schemaVersion": 38,
  "style": "dark",
  "tags": [
    "django",
    "vm"
  ],
  "templating": {
    "list": []
  },
  "time": {
    "from": "now-5m",
    "to": "now"
  },
  "timepicker": {},
  "timezone": "browser",
  "title": "Django VM Dashboard",
  "uid": "django-vm-dashboard",
  "version": 1,
  "weekStart": ""
}
EOF

echo "🔄 Restarting Grafana to apply changes..."
docker-compose restart grafana

echo "⏳ Waiting for Grafana to start..."
sleep 10

echo "🧪 Testing connections..."
echo "1. Testing Prometheus connection:"
if curl -s http://localhost:9090/-/ready > /dev/null; then
    echo "   ✅ Prometheus is accessible"
else
    echo "   ❌ Prometheus is not accessible"
fi

echo "2. Testing Django metrics endpoint:"
if curl -s http://localhost:8000/metrics > /dev/null; then
    echo "   ✅ Django metrics endpoint is accessible"
else
    echo "   ❌ Django metrics endpoint is not accessible"
fi

echo "3. Testing Grafana:"
if curl -s http://localhost:3000/api/health > /dev/null; then
    echo "   ✅ Grafana is accessible"
else
    echo "   ❌ Grafana is not accessible"
fi

echo ""
echo "🎯 Next Steps:"
echo "1. Access Grafana at: http://localhost:3000"
echo "2. Login with: aziz/aziz123"
echo "3. Look for 'Django VM Dashboard' in the dashboards"
echo "4. If no data appears, check Prometheus targets at: http://localhost:9090/targets"
echo ""
echo "🔍 Troubleshooting:"
echo "- Check logs: docker-compose logs grafana"
echo "- Check Prometheus targets: http://localhost:9090/targets"
echo "- Verify Django metrics: curl http://localhost:8000/metrics"
EOF
