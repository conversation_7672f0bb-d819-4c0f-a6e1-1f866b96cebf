<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>My Google Map</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyBAGZDj8MzsboDodMdjrAhjIfSKPQ2vrUo"></script>
    <script>
      function initMap() {
        var location = { lat: 36.8270, lng: 10.1658 };  // BP 762 Ave Mohamed Ali Akid
        var map = new google.maps.Map(document.getElementById('map'), {
          zoom: 15,
          center: location
        });
        var marker = new google.maps.Marker({
          position: location,
          map: map,
          title: 'BP 762 Ave Mohamed Ali Akid, Tunis 1003'
        });
      }
    </script>
</head>
<body onload="initMap()">
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container mt-4">
            <a class="navbar-brand" href="{% url 'home' %}">Home</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item"><a class="nav-link" href="{% url 'order_list' %}">Commandes</a></li>
                    <li class="nav-item"><a class="nav-link" href="{% url 'product_list' %}">Produits</a></li>
                    <li class="nav-item"><a class="nav-link" href="{% url 'supplier_list' %}">Fournisseurs</a></li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-5">
        <h1 class="text-center mb-4">My Destination</h1>
        <div id="map" style="height: 500px; width: 100%;"></div>
    </div>

    <!-- Bootstrap JS (for the navbar toggler to work) -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
