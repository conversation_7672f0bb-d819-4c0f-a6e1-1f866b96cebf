<div class="mb-3">
    <label for="id_status" class="form-label">Statut</label>
    {{ form.status }}
</div>

<!-- Hidden field for quantities -->
<input type="hidden" name="quantities" id="quantities" value="">

<h5 class="mt-4 mb-3">Sélectionner les produits</h5>
<div class="row row-cols-1 row-cols-md-2 g-4">
    {% for product in products %}
    <div class="col">
        <div class="card product-card h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-start mb-2">
                    <div class="form-check">
                        <input class="form-check-input product-checkbox" type="checkbox" 
                               name="products" value="{{ product.id }}" id="product{{ product.id }}">
                        <label class="form-check-label" for="product{{ product.id }}">
                            <strong>{{ product.name }}</strong>
                        </label>
                    </div>
                </div>
                <div class="product-info">
                    <div><small class="text-muted">Catégorie: {{ product.category }}</small></div>
                    <div><small class="text-muted">Prix: {{ product.price }}€</small></div>
                    <div><small class="text-muted">Stock: {{ product.quantity_in_stock }}</small></div>
                </div>
                <div class="quantity-control mt-2" style="display: none;">
                    <label class="form-label">Quantité:</label>
                    <div class="input-group input-group-sm">
                        <button type="button" class="btn btn-outline-secondary decrease-qty">-</button>
                        <input type="number" class="form-control text-center quantity-input" 
                               value="1" min="1" max="{{ product.quantity_in_stock }}">
                        <button type="button" class="btn btn-outline-secondary increase-qty">+</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endfor %}
</div>

<script>
    // Initialize the form controls after the content is loaded
    (function() {
        const checkboxes = document.querySelectorAll('.product-checkbox');
        const quantitiesInput = document.getElementById('quantities');
        
        // Show/hide quantity controls when product is selected
        checkboxes.forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                const card = this.closest('.card');
                const quantityControl = card.querySelector('.quantity-control');
                
                if (this.checked) {
                    quantityControl.style.display = 'block';
                } else {
                    quantityControl.style.display = 'none';
                }
                
                updateQuantitiesField();
            });
        });
        
        // Increase quantity button
        document.querySelectorAll('.increase-qty').forEach(btn => {
            btn.addEventListener('click', function() {
                const input = this.parentElement.querySelector('.quantity-input');
                let value = parseInt(input.value);
                let max = parseInt(input.getAttribute('max'));
                
                if (value < max) {
                    input.value = value + 1;
                    updateQuantitiesField();
                }
            });
        });
        
        // Decrease quantity button
        document.querySelectorAll('.decrease-qty').forEach(btn => {
            btn.addEventListener('click', function() {
                const input = this.parentElement.querySelector('.quantity-input');
                let value = parseInt(input.value);
                
                if (value > 1) {
                    input.value = value - 1;
                    updateQuantitiesField();
                }
            });
        });
        
        // Handle direct input changes
        document.querySelectorAll('.quantity-input').forEach(input => {
            input.addEventListener('change', function() {
                validateQuantityInput(this);
                updateQuantitiesField();
            });
            
            input.addEventListener('input', function() {
                validateQuantityInput(this);
                updateQuantitiesField();
            });
        });
        
        // Validate quantity input
        function validateQuantityInput(input) {
            let value = parseInt(input.value);
            let max = parseInt(input.getAttribute('max'));
            
            if (isNaN(value) || value < 1) {
                input.value = 1;
            } else if (value > max) {
                input.value = max;
            }
        }
        
        // Update hidden quantities field
        function updateQuantitiesField() {
            const quantities = [];
            
            checkboxes.forEach(checkbox => {
                if (checkbox.checked) {
                    const productId = checkbox.value;
                    const card = checkbox.closest('.card');
                    const quantityInput = card.querySelector('.quantity-input');
                    quantities.push(`${productId}:${quantityInput.value}`);
                }
            });
            
            quantitiesInput.value = quantities.join(',');
            console.log("Updated quantities:", quantitiesInput.value); // Debug log
        }
        
        // Initialize quantities field
        updateQuantitiesField();

        // Debug: Log when form is ready
        console.log("Order form initialized with", checkboxes.length, "products");
    })(); // Immediately invoke the function
</script>



