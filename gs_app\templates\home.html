{% load static %}
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SNDP Agil - Gestion de Stock Pétrolier</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="{% static 'css/home.css' %}" rel="stylesheet">


</head>
<body>

<nav class="navbar navbar-expand-lg navbar-dark">
    <div class="container">
        <a class="navbar-brand" href="{% url 'home' %}">SNDP Agil</a>
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav ms-auto">
                <li class="nav-item"><a class="nav-link" href="{% url 'product_list' %}">Produits </a></li>
                <li class="nav-item"><a class="nav-link" href="{% url 'order_list' %}">Commandes</a></li>
                <li class="nav-item"><a class="nav-link" href="{% url 'supplier_list' %}">Fournisseurs</a></li>
                <li class="nav-item"><a class="nav-link" href="{% url 'upload_excel' %}">Ask AI</a></li>

                {% if user.is_superuser %}
                    <li class="nav-item"><a class="nav-link" href="{% url 'admin:index' %}">Admin</a></li>
                {% endif %}

                <li class="nav-item">
                    {% if user.is_authenticated %}
                        <form method="post" action="{% url 'logout' %}" class="d-inline">
                            {% csrf_token %}
                            <button type="submit" class="nav-link btn btn-link" style="color: #fff !important; padding: 0.5rem 1rem; margin: 0; border: none; background: none; text-decoration: none;">
                                Déconnexion
                            </button>
                        </form>
                    {% else %}
                        <a class="nav-link" href="{% url 'login' %}">Connexion</a>
                    {% endif %}
                </li>
            </ul>
        </div>
    </div>
</nav>

    <!-- Hero Section with Main Image -->
    <div class="hero-section">
        <div class="container">
            <div class="hero-content">
                <h1>Bienvenue, {{ user.username }} !</h1>
                <p>Optimisez la gestion de vos stocks pétroliers avec une solution moderne et intuitive</p>
                <img src="{% static '1.png' %}" alt="SNDP Agile" class="img-fluid">
                <br>
                <a href="{% url 'dashboard' %}" class="btn-hero">Accéder au Dashboard</a>
            </div>
        </div>
    </div>

    <!-- Info Section with Cards -->
    <div class="info-section">
        <div class="container">
            <div class="section-title">
                <h2>Solutions de Gestion Avancées</h2>
                <p>Découvrez nos fonctionnalités conçues pour optimiser votre gestion de stock pétrolier</p>
            </div>
            <div class="row g-4">
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <img src="{% static 'ges.png' %}" alt="Gestion Efficace">
                        <h5>Gestion Efficace</h5>
                        <p>Optimisez vos stocks de produits pétroliers avec des outils intelligents qui réduisent les coûts et améliorent l'efficacité opérationnelle.</p>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <img src="{% static 'suivi.png' %}" alt="Suivi en Temps Réel">
                        <h5>Suivi en Temps Réel</h5>
                        <p>Surveillez vos stocks de carburants et autres produits pétroliers en temps réel avec des tableaux de bord interactifs et des alertes automatiques.</p>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <img src="{% static 'rapport.png' %}" alt="Rapports Personnalisés">
                        <h5>Rapports Personnalisés</h5>
                        <p>Générez des rapports détaillés et des analyses approfondies pour une meilleure prise de décision dans la gestion de vos stocks pétroliers.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <!-- Contact Information -->
                <div class="col-lg-4 col-md-6 mb-4">
                    <h5>Nous Contacter</h5>
                    <p>
                        <strong>Téléphone :</strong> +216 123 456 789<br>
                        <strong>Email :</strong> <a href="mailto:<EMAIL>"><EMAIL></a>
                    </p>
                </div>

                <!-- Address -->
                <div class="col-lg-4 col-md-6 mb-4">
                    <h5>Notre Adresse</h5>
                    <p>
                        BP 762 Ave Mohamed Ali Akid<br>
                        Tunis 1003, Tunisie
                    </p>
                </div>

                <!-- Links -->
                <div class="col-lg-4 col-md-6 mb-4">
                    <h5>Liens Utiles</h5>
                    <div class="d-flex flex-column gap-2">
                        <a href="{% url 'map' %}" class="btn btn-outline-light">Voir la Carte</a>
                        <a href="{% url 'dashboard' %}" class="btn btn-outline-light">Dashboard</a>
                    </div>
                </div>
            </div>

            <hr style="border-color: rgba(255, 255, 255, 0.2); margin: 2rem 0 1rem;">

            <div class="text-center">
                <p>© 2025 SNDP Agile. Tous droits réservés.</p>
            </div>
        </div>
    </footer>

    <!-- Modern Chat Interface -->
    <div id="chat-container">
        <div id="chat-header">
            <h5>Assistant SNDP Agile</h5>
            <button id="toggle-chat">−</button>
        </div>
        <div id="chat-messages"></div>
        <div id="chat-input-container">
            <input type="text" id="chat-input" placeholder="Tapez votre message..." />
            <button id="send-button">→</button>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const chatContainer = document.getElementById('chat-container');
            const chatMessages = document.getElementById('chat-messages');
            const chatInput = document.getElementById('chat-input');
            const sendButton = document.getElementById('send-button');
            const toggleButton = document.getElementById('toggle-chat');

            let isChatMinimized = false;

            // Helper function to get CSRF token
            function getCookie(name) {
                let cookieValue = null;
                if (document.cookie && document.cookie !== '') {
                    const cookies = document.cookie.split(';');
                    for (let i = 0; i < cookies.length; i++) {
                        const cookie = cookies[i].trim();
                        if (cookie.substring(0, name.length + 1) === (name + '=')) {
                            cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                            break;
                        }
                    }
                }
                return cookieValue;
            }

            // Toggle chat visibility
            toggleButton.addEventListener('click', () => {
                isChatMinimized = !isChatMinimized;
                if (isChatMinimized) {
                    chatMessages.style.display = 'none';
                    document.getElementById('chat-input-container').style.display = 'none';
                    toggleButton.textContent = '+';
                    chatContainer.style.height = 'auto';
                } else {
                    chatMessages.style.display = 'block';
                    document.getElementById('chat-input-container').style.display = 'flex';
                    toggleButton.textContent = '−';
                    chatContainer.style.height = 'auto';
                }
            });

            // Add welcome message
            addBotMessage("Bonjour! Je suis l'assistant SNDP Agile. Comment puis-je vous aider avec la gestion de vos stocks pétroliers?");

            // Send message function
            function sendMessage() {
                const message = chatInput.value.trim();
                if (!message) return;

                // Add user message
                addUserMessage(message);
                chatInput.value = '';

                // Show typing indicator
                const typingIndicator = addTypingIndicator();

                // Send to Django Ollama endpoint
                fetch('{% url "ollama_chat" %}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': getCookie('csrftoken')
                    },
                    body: JSON.stringify({
                        message: message
                    })
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    // Remove typing indicator
                    chatMessages.removeChild(typingIndicator);

                    // Check if there's an error in the response
                    if (data.error) {
                        addBotMessage(`Erreur: ${data.error}`);
                    } else {
                        // Add bot response
                        addBotMessage(data.message.content);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    chatMessages.removeChild(typingIndicator);
                    addBotMessage("Désolé, je ne peux pas répondre pour le moment. Erreur de connexion: " + error.message);
                });
            }

            // Helper functions for messages
            function addUserMessage(text) {
                const messageDiv = document.createElement('div');
                messageDiv.className = 'message user-message';
                messageDiv.innerHTML = `<span>${text}</span>`;
                chatMessages.appendChild(messageDiv);
                chatMessages.scrollTop = chatMessages.scrollHeight;
            }

            function addBotMessage(text) {
                const messageDiv = document.createElement('div');
                messageDiv.className = 'message bot-message';
                messageDiv.innerHTML = `<span>${text}</span>`;
                chatMessages.appendChild(messageDiv);
                chatMessages.scrollTop = chatMessages.scrollHeight;
            }

            function addTypingIndicator() {
                const typingDiv = document.createElement('div');
                typingDiv.className = 'typing-indicator';
                typingDiv.textContent = "L'assistant tape...";
                chatMessages.appendChild(typingDiv);
                chatMessages.scrollTop = chatMessages.scrollHeight;
                return typingDiv;
            }

            // Event listeners
            sendButton.addEventListener('click', sendMessage);
            chatInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') sendMessage();
            });
        });
    </script>
</body>
</html>