{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Stock Advisor</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css">
    <link href="{% static 'css/upload.css' %}" rel="stylesheet">
</head>
<body>
    <!-- Navigation Bar -->
    <nav class="navbar navbar-expand-lg shadow-sm">
        <div class="container">
            <a class="navbar-brand d-flex align-items-center" href="{% url 'home' %}">
                <span class="fw-bold text-primary">SNDP</span>
                <span class="ms-1">Agil</span>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav mx-auto">
                    <li class="nav-item mx-2">
                        <a class="nav-link" href="{% url 'product_list' %}">
                            <i class="bi bi-box me-1"></i>Produits
                        </a>
                    </li>
                    <li class="nav-item mx-2">
                        <a class="nav-link" href="{% url 'order_list' %}">
                            <i class="bi bi-cart me-1"></i>Commandes
                        </a>
                    </li>
                    <li class="nav-item mx-2">
                        <a class="nav-link" href="{% url 'supplier_list' %}">
                            <i class="bi bi-building me-1"></i>Fournisseurs
                        </a>
                    </li>
                    <li class="nav-item mx-2">
                        <a class="nav-link active" href="{% url 'upload_excel' %}">
                            <i class="bi bi-robot me-1"></i>Ask AI
                        </a>
                    </li>
                </ul>
                <div class="dark-mode-toggle-container d-flex align-items-center">
                    <label class="dark-mode-toggle me-2">
                        <input type="checkbox" id="toggleSwitch">
                        <span class="slider"></span>
                    </label>
                    <span class="text-nowrap fs-sm">Mode Sombre</span>
                </div>
            </div>
        </div>
    </nav>

    <!-- Header Section -->
    <div class="header-section">
        <div class="container">
            <div class="header-content text-center">
                <h1>
                    <i class="bi bi-file-earmark-excel me-2"></i>
                    Analyse Intelligente de Stock
                </h1>
                <p>Téléchargez votre fichier Excel pour une analyse IA avancée</p>
            </div>
        </div>
    </div>

    <!-- Stock Analysis Form -->
    <div class="container main-container">
        <h1 class="text-center">Upload Excel for Stock Analysis</h1>
        <form method="post" enctype="multipart/form-data">
            {% csrf_token %}
            <div class="form-group mb-3">
                <label for="excel_file">Choose Excel File</label>
                <input type="file" class="form-control" id="excel_file" name="excel_file" required>
            </div>
            <button type="submit" class="btn btn-primary w-100">Analyze</button>
        </form>
        {% if advice %}
            <div class="result">
                <h2>AI Advice:</h2>
                <pre>{{ advice }}</pre>
            </div>
        {% endif %}
    </div>

    <!-- Footer -->
    <footer>
        <p>© 2025 Gestion des Commandes - All rights reserved.</p>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Dark Mode Toggle
        const html = document.documentElement;
        const toggleSwitch = document.getElementById('toggleSwitch');

        // Load dark mode preference
        if (localStorage.getItem('darkMode') === 'true') {
            html.classList.add('dark-mode');
            toggleSwitch.checked = true;
        }

        // Toggle dark mode
        toggleSwitch.addEventListener('change', () => {
            html.classList.toggle('dark-mode');
            localStorage.setItem('darkMode', html.classList.contains('dark-mode'));
        });
    </script>
</body>
</html>