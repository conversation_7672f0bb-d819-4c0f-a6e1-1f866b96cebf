{{ form.as_p }}

{% if product and product.quantity_in_stock < 100 %}
<div class="alert alert-warning">
    <strong>Attention!</strong> Stock faible ({{ product.quantity_in_stock }} unités).
</div>
{% endif %}

<script>
    // This script will run when the form is loaded via AJAX
    (function() {
        const quantityInput = document.querySelector('input[name="quantity_in_stock"]');
        const restockField = document.getElementById('restockField{{ product.id }}');
        
        if (quantityInput && restockField) {
            // Find the form containing this input
            const form = quantityInput.closest('form');
            
            // Remove any existing submit event listeners to avoid duplicates
            const clonedForm = form.cloneNode(true);
            form.parentNode.replaceChild(clonedForm, form);
            
            // Add the event listener to the form
            clonedForm.addEventListener('submit', function(e) {
                const currentQuantity = parseInt(quantityInput.value);
                
                if (currentQuantity < 100) {
                    const confirmRestock = confirm('Quantité faible (' + currentQuantity + '). Voulez-vous réapprovisionner (+100)?');
                    
                    if (confirmRestock) {
                        restockField.value = 'yes';
                    } else {
                        restockField.value = 'no';
                    }
                }
            });
        }
    })();
</script>
