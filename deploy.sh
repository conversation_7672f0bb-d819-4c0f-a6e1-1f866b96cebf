#!/bin/bash

# SNDP Agile Application Deployment Script
# This script can be used by <PERSON> or run manually

set -e  # Exit on any error

# Configuration
APP_NAME="sndp-agile-app"
DEPLOYMENT_DIR="/opt/sndp-agile"
DOCKER_COMPOSE_FILE="docker-compose.yml"
BACKUP_DIR="/opt/backups/sndp-agile"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Check if running as root or with sudo
check_permissions() {
    if [[ $EUID -eq 0 ]]; then
        warning "Running as root. Consider using a dedicated user."
    fi
}

# Check system requirements
check_requirements() {
    log "Checking system requirements..."
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        error "Docker is not installed or not in PATH"
        exit 1
    fi
    
    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        error "Docker Compose is not installed or not in PATH"
        exit 1
    fi
    
    # Check available disk space (at least 5GB)
    available_space=$(df / | awk 'NR==2 {print $4}')
    if [ "$available_space" -lt 5242880 ]; then  # 5GB in KB
        warning "Less than 5GB disk space available"
    fi
    
    # Check available memory (at least 4GB)
    available_memory=$(free -m | awk 'NR==2{print $7}')
    if [ "$available_memory" -lt 4096 ]; then
        warning "Less than 4GB memory available"
    fi
    
    success "System requirements check completed"
}

# Create backup of current deployment
create_backup() {
    if [ -d "$DEPLOYMENT_DIR" ]; then
        log "Creating backup of current deployment..."
        
        # Create backup directory
        mkdir -p "$BACKUP_DIR"
        
        # Create timestamped backup
        backup_name="backup-$(date +%Y%m%d-%H%M%S)"
        
        # Stop containers before backup
        cd "$DEPLOYMENT_DIR"
        docker-compose down || true
        
        # Create backup
        tar -czf "$BACKUP_DIR/$backup_name.tar.gz" -C "$(dirname $DEPLOYMENT_DIR)" "$(basename $DEPLOYMENT_DIR)"
        
        success "Backup created: $BACKUP_DIR/$backup_name.tar.gz"
        
        # Keep only last 5 backups
        cd "$BACKUP_DIR"
        ls -t *.tar.gz | tail -n +6 | xargs -r rm --
    fi
}

# Setup deployment directory
setup_deployment_dir() {
    log "Setting up deployment directory..."
    
    # Create deployment directory
    mkdir -p "$DEPLOYMENT_DIR"
    
    # Set proper permissions
    if [ "$USER" != "root" ]; then
        sudo chown -R "$USER:$USER" "$DEPLOYMENT_DIR"
    fi
    
    success "Deployment directory ready: $DEPLOYMENT_DIR"
}

# Deploy application
deploy_application() {
    log "Deploying application..."
    
    cd "$DEPLOYMENT_DIR"
    
    # Check if docker-compose file exists
    if [ ! -f "$DOCKER_COMPOSE_FILE" ]; then
        error "Docker Compose file not found: $DOCKER_COMPOSE_FILE"
        exit 1
    fi
    
    # Validate docker-compose file
    log "Validating Docker Compose configuration..."
    docker-compose config > /dev/null
    
    # Pull latest images
    log "Pulling latest Docker images..."
    docker-compose pull || warning "Some images could not be pulled"
    
    # Build application
    log "Building application..."
    docker-compose build --no-cache
    
    # Start services
    log "Starting services..."
    docker-compose up -d
    
    success "Application deployed successfully"
}

# Health check
health_check() {
    log "Performing health checks..."
    
    cd "$DEPLOYMENT_DIR"
    
    # Wait for services to start
    sleep 30
    
    # Check container status
    log "Checking container status..."
    docker-compose ps
    
    # Test Django application
    log "Testing Django application..."
    timeout=60
    counter=0
    
    while [ $counter -lt $timeout ]; do
        if curl -f -s http://localhost:8000 > /dev/null 2>&1; then
            success "Django application is responding!"
            break
        fi
        log "Waiting for Django app... ($counter/$timeout seconds)"
        sleep 5
        counter=$((counter + 5))
    done
    
    if [ $counter -ge $timeout ]; then
        error "Django application failed to start within $timeout seconds"
        docker-compose logs web
        return 1
    fi
    
    # Test other services
    curl -f -s http://localhost:9090 > /dev/null 2>&1 && success "Prometheus is working" || warning "Prometheus not responding"
    curl -f -s http://localhost:3000 > /dev/null 2>&1 && success "Grafana is working" || warning "Grafana not responding"
    curl -f -s http://localhost:8000/metrics > /dev/null 2>&1 && success "Metrics endpoint working" || warning "Metrics endpoint not responding"
    
    success "Health checks completed"
}

# Show deployment status
show_status() {
    log "Deployment Status:"
    echo "===================="
    echo "🌐 Application URLs:"
    echo "   - Django App:    http://localhost:8000"
    echo "   - Grafana:       http://localhost:3000 (aziz/aziz123)"
    echo "   - Prometheus:    http://localhost:9090"
    echo "   - Metrics:       http://localhost:8000/metrics"
    echo ""
    echo "📊 Container Status:"
    cd "$DEPLOYMENT_DIR"
    docker-compose ps
    echo ""
    echo "💾 Disk Usage:"
    df -h /
    echo ""
    echo "🧠 Memory Usage:"
    free -h
}

# Rollback function
rollback() {
    log "Rolling back to previous version..."
    
    # Find latest backup
    latest_backup=$(ls -t "$BACKUP_DIR"/*.tar.gz 2>/dev/null | head -n1)
    
    if [ -z "$latest_backup" ]; then
        error "No backup found for rollback"
        exit 1
    fi
    
    log "Rolling back to: $latest_backup"
    
    # Stop current deployment
    cd "$DEPLOYMENT_DIR"
    docker-compose down || true
    
    # Remove current deployment
    cd /
    rm -rf "$DEPLOYMENT_DIR"
    
    # Restore backup
    mkdir -p "$(dirname $DEPLOYMENT_DIR)"
    tar -xzf "$latest_backup" -C "$(dirname $DEPLOYMENT_DIR)"
    
    # Start restored deployment
    cd "$DEPLOYMENT_DIR"
    docker-compose up -d
    
    success "Rollback completed"
}

# Main deployment function
main() {
    log "Starting SNDP Agile Application Deployment"
    
    check_permissions
    check_requirements
    
    # Handle command line arguments
    case "${1:-deploy}" in
        "deploy")
            create_backup
            setup_deployment_dir
            deploy_application
            health_check
            show_status
            ;;
        "rollback")
            rollback
            health_check
            show_status
            ;;
        "status")
            show_status
            ;;
        "health")
            health_check
            ;;
        *)
            echo "Usage: $0 {deploy|rollback|status|health}"
            echo "  deploy   - Deploy the application (default)"
            echo "  rollback - Rollback to previous version"
            echo "  status   - Show current deployment status"
            echo "  health   - Run health checks"
            exit 1
            ;;
    esac
    
    success "Deployment script completed successfully!"
}

# Run main function
main "$@"
