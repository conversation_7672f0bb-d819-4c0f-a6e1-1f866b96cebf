# Jenkins Container Setup for SNDP Agile Deployment

This guide shows how to set up <PERSON> in a container to deploy your SNDP Agile application using the host machine's Docker and Docker Compose.

## 🎯 Architecture Overview

```
┌─────────────────────────────────────────────────────────────┐
│                        VM Host                              │
│  ┌─────────────────┐    ┌─────────────────────────────────┐ │
│  │ Jenkins         │    │ SNDP Agile Application          │ │
│  │ Container       │    │ (Docker Compose)                │ │
│  │                 │    │                                 │ │
│  │ - Accesses host │───▶│ - Django Web App                │ │
│  │   Docker socket │    │ - PostgreSQL DB                 │ │
│  │ - Uses host     │    │ - Ollama AI Service             │ │
│  │   Docker Compose│    │ - Prometheus Monitoring         │ │
│  │ - Deploys to    │    │ - Grafana Dashboard             │ │
│  │   /opt/sndp-agile   │                                 │ │
│  └─────────────────┘    └─────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 🐳 Jenkins Container Setup

### 1. **Create Jenkins Docker Compose**

Create `jenkins-docker-compose.yml`:

```yaml
version: '3.8'

services:
  jenkins:
    image: jenkins/jenkins:lts
    container_name: jenkins_server
    user: root  # Required for Docker socket access
    ports:
      - "8080:8080"
      - "50000:50000"
    volumes:
      # Jenkins data persistence
      - jenkins_data:/var/jenkins_home
      
      # Mount Docker socket for host Docker access
      - /var/run/docker.sock:/var/run/docker.sock
      
      # Mount Docker binary
      - /usr/bin/docker:/usr/bin/docker:ro
      - /usr/local/bin/docker-compose:/usr/local/bin/docker-compose:ro
      
      # Mount host deployment directory
      - /opt:/opt
      
      # Mount host filesystem for file operations
      - /:/host:ro
    environment:
      - DOCKER_HOST=unix:///var/run/docker.sock
      - JENKINS_OPTS=--httpPort=8080
    restart: unless-stopped
    networks:
      - jenkins-network

volumes:
  jenkins_data:

networks:
  jenkins-network:
    driver: bridge
```

### 2. **Start Jenkins Container**

```bash
# Create the docker-compose file
sudo mkdir -p /opt/jenkins
cd /opt/jenkins

# Copy the jenkins-docker-compose.yml content above to this file
sudo nano jenkins-docker-compose.yml

# Start Jenkins
sudo docker-compose -f jenkins-docker-compose.yml up -d

# Check if Jenkins is running
sudo docker-compose -f jenkins-docker-compose.yml ps
```

### 3. **Initial Jenkins Setup**

```bash
# Get initial admin password
sudo docker exec jenkins_server cat /var/jenkins_home/secrets/initialAdminPassword

# Access Jenkins at: http://YOUR-VM-IP:8080
# Use the password from above to complete setup
```

## 🔧 Jenkins Configuration

### 1. **Install Required Plugins**

Go to **Manage Jenkins** → **Manage Plugins** → **Available**:

- ✅ **Docker Pipeline Plugin**
- ✅ **Git Plugin** (usually pre-installed)
- ✅ **Pipeline Plugin** (usually pre-installed)
- ✅ **GitLab Plugin**
- ✅ **Credentials Plugin** (usually pre-installed)

### 2. **Configure GitLab Credentials**

1. **Manage Jenkins** → **Manage Credentials** → **(global)**
2. **Add Credentials** → **Username with password**:
   - **Username**: Your GitLab username
   - **Password**: Your GitLab Personal Access Token
   - **ID**: `gitlab-token`
   - **Description**: `GitLab Access Token`

### 3. **Update Jenkinsfile Repository URL**

Edit your `Jenkinsfile` and update the repository URL:
```groovy
url: 'https://gitlab.com/YOUR-USERNAME/YOUR-REPO-NAME.git'
```

### 4. **Create Pipeline Job**

1. **New Item** → **Pipeline** → Name: `sndp-agile-deployment`
2. **Pipeline Configuration**:
   - **Definition**: Pipeline script from SCM
   - **SCM**: Git
   - **Repository URL**: Your GitLab repo URL
   - **Credentials**: Select `gitlab-token`
   - **Branch**: `*/main`
   - **Script Path**: `Jenkinsfile`

## 🎯 Key Features of Container Setup

### **✅ Host Docker Access**
- Jenkins container uses host Docker daemon
- No Docker-in-Docker complexity
- Direct access to host Docker Compose

### **✅ File System Access**
- `/opt` mounted for deployment directory
- Direct file copying to host filesystem
- Persistent deployment location

### **✅ Network Connectivity**
- Jenkins can test deployed services
- Multiple host resolution methods
- Proper container-to-host communication

### **✅ Resource Efficiency**
- Single Docker daemon for all containers
- No nested virtualization overhead
- Shared host resources

## 🔍 Pipeline Stages Explained

### **🔍 Environment Check**
- Verifies Docker socket access from container
- Tests Docker and Docker Compose availability
- Checks host directory mounting

### **📥 Clone Repository**
- Clones your private GitLab repository
- Uses configured credentials
- Clean workspace management

### **🔧 Pre-deployment Setup**
- Creates deployment directory on host
- Stops existing containers safely
- Cleans up old Docker images

### **📦 Build Application**
- Copies source code to host deployment directory
- Builds Docker images using host Docker
- Handles both mounted and volume-based file transfer

### **🧪 Health Check**
- Validates Docker Compose configuration
- Verifies required files on host
- Pre-deployment validation

### **🚀 Deploy Application**
- Starts services using host Docker Compose
- Proper project directory handling
- Container status monitoring

### **✅ Deployment Verification**
- Tests application endpoints from container
- Multiple host resolution strategies
- Comprehensive service verification

## 🛠️ Troubleshooting

### **Docker Socket Permission Issues**
```bash
# Check Docker socket permissions
ls -la /var/run/docker.sock

# Fix permissions if needed
sudo chmod 666 /var/run/docker.sock

# Or add jenkins user to docker group (inside container)
sudo docker exec -u root jenkins_server usermod -aG docker jenkins
```

### **File Permission Issues**
```bash
# Fix deployment directory permissions
sudo chown -R 1000:1000 /opt/sndp-agile
sudo chmod -R 755 /opt/sndp-agile
```

### **Network Connectivity Issues**
```bash
# Test connectivity from Jenkins container
sudo docker exec jenkins_server curl -I http://host.docker.internal:8000
sudo docker exec jenkins_server curl -I http://**********:8000
sudo docker exec jenkins_server curl -I http://localhost:8000
```

### **Docker Compose Path Issues**
```bash
# Check if docker-compose is accessible
sudo docker exec jenkins_server which docker-compose
sudo docker exec jenkins_server docker-compose --version

# If not found, install in container
sudo docker exec -u root jenkins_server apt-get update
sudo docker exec -u root jenkins_server apt-get install -y docker-compose-plugin
```

## 📊 Monitoring and Logs

### **Jenkins Logs**
```bash
# View Jenkins container logs
sudo docker logs -f jenkins_server

# View specific build logs in Jenkins UI
# Go to: Build → Console Output
```

### **Application Logs**
```bash
# View deployed application logs
cd /opt/sndp-agile
sudo docker-compose logs -f

# View specific service logs
sudo docker-compose logs -f web
sudo docker-compose logs -f db
```

### **System Monitoring**
```bash
# Check container resource usage
sudo docker stats

# Check disk usage
df -h /opt

# Check memory usage
free -h
```

## 🚀 Testing the Setup

1. **Start Jenkins**: `sudo docker-compose -f jenkins-docker-compose.yml up -d`
2. **Access Jenkins**: http://YOUR-VM-IP:8080
3. **Create Pipeline Job** with your GitLab repository
4. **Run Build**: Click "Build Now"
5. **Monitor Progress**: Watch console output
6. **Verify Deployment**: Check application at http://YOUR-VM-IP:8000

## 🔄 Automatic Deployments

### **GitLab Webhook Setup**
1. GitLab Project → **Settings** → **Webhooks**
2. **URL**: `http://YOUR-VM-IP:8080/project/sndp-agile-deployment`
3. **Trigger**: Push events
4. **Secret Token**: (optional, configure in Jenkins)

### **Jenkins Polling** (Alternative)
1. Job Configuration → **Build Triggers**
2. **Poll SCM**: `H/5 * * * *` (every 5 minutes)

Your Jenkins container setup is now ready to deploy your SNDP Agile application using the host machine's Docker! 🎉
