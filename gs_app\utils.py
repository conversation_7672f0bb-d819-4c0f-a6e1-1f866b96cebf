import pandas as pd
import requests

def prepare_prompt_from_df(df):
    if 'quantity' in df.columns and 'min_required' in df.columns:
        low_stock = df[df['quantity'] < df['min_required']]
        summary = low_stock[['product_name', 'quantity', 'min_required']].to_dict(orient='records')
    else:
        summary = df.head(5).to_dict(orient='records')

    prompt = f"""
You are an inventory advisor. Based on the data below, advise which items need restocking and why:

{summary}

Give suggestions on what to reorder, how much, and any optimization tips.
"""
    return prompt

def translate_fr_to_en(text):
    """
    Translate French text to English using an external API
    
    Args:
        text (str): The French text to translate
        
    Returns:
        str: The translated English text, or the original text if translation fails
    """
    try:
        # Using a free translation API
        url = "https://translate.googleapis.com/translate_a/single"
        params = {
            "client": "gtx",
            "sl": "fr",  # Source language: French
            "tl": "en",  # Target language: English
            "dt": "t",   # Return translated text
            "q": text    # Text to translate
        }
        
        response = requests.get(url, params=params)
        if response.status_code == 200:
            # Parse the response
            result = response.json()
            translated_text = ''.join([sentence[0] for sentence in result[0]])
            return translated_text
        else:
            return text  # Return original text if API call fails
    except Exception as e:
        # Log the error but return the original text to avoid breaking the app
        print(f"Translation error: {str(e)}")
        return text
