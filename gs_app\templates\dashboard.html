{% load static %}
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Tableau de Bord | Gestion de Stock</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <link href="{% static 'css/dashboard.css' %}" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <!-- Navigation Bar -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand d-flex align-items-center" href="{% url 'home' %}">
                <i class="bi bi-fuel-pump me-2"></i>
                <span class="fw-bold">SNDP</span>
                <span class="ms-1">Agil</span>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="{% url 'dashboard' %}">
                            <i class="bi bi-speedometer2 me-1"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'order_list' %}">
                            <i class="bi bi-cart3 me-1"></i> Commandes
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'product_list' %}">
                            <i class="bi bi-box-seam me-1"></i> Produits
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'supplier_list' %}">
                            <i class="bi bi-building me-1"></i> Fournisseurs
                        </a>
                    </li>
                </ul>
                <div class="dark-mode-toggle-container">
                    <label class="dark-mode-toggle">
                        <input type="checkbox" id="toggleSwitch">
                        <span class="slider"></span>
                    </label>
                    <span class="dark-mode-toggle-label">Mode Sombre</span>
                </div>
            </div>
        </div>
    </nav>

    <!-- Header Section -->
    <div class="header-section">
        <div class="container">
            <div class="header-content text-center">
                <h1>
                    <i class="bi bi-speedometer2 me-2"></i>
                    Tableau de Bord
                </h1>
                <p>Vue d'ensemble de votre gestion de stock pétrolier</p>
            </div>
        </div>
    </div>

    <!-- Dashboard Content -->
    <div class="container main-container">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div class="d-flex align-items-center">
                <span class="me-2">Dernière mise à jour: {{ now|date:"d M Y H:i" }}</span>
                <button class="btn btn-sm btn-outline-primary" onclick="window.location.reload()">
                    <i class="bi bi-arrow-clockwise"></i> Actualiser
                </button>
            </div>
        </div>

        <!-- Quick Action Buttons -->
        <div class="quick-actions mb-4">
            <a href="{% url 'product_list' %}?filter=low_stock" class="quick-action-btn text-decoration-none text-warning">
                <i class="bi bi-exclamation-triangle"></i>
                <span>Stock Faible</span>
            </a>
        </div>

        <!-- Summary Cards -->
        <div class="stats-grid">
            <div class="stat-card">
                <i class="stat-icon bi bi-box-seam text-primary"></i>
                <div class="stat-number">{{ total_products }}</div>
                <div class="stat-label">Total des Produits</div>
                <div class="d-flex justify-content-between align-items-center mt-3">
                    <small class="text-success">
                        <i class="bi bi-check-circle-fill"></i> {{ products_in_stock }} en stock
                    </small>
                    <small class="text-danger">
                        <i class="bi bi-x-circle-fill"></i> {{ out_of_stock }} en rupture
                    </small>
                </div>
            </div>
            <div class="stat-card">
                <i class="stat-icon bi bi-cart3 text-success"></i>
                <div class="stat-number">{{ total_orders }}</div>
                <div class="stat-label">Total des Commandes</div>
                <div class="progress mt-3">
                    <div class="progress-bar bg-success" role="progressbar" style="width: 75%" aria-valuenow="75" aria-valuemin="0" aria-valuemax="100"></div>
                </div>
                <small class="text-muted mt-2 d-block">+15% par rapport au mois dernier</small>
            </div>
            <div class="stat-card">
                <i class="stat-icon bi bi-building text-info"></i>
                <div class="stat-number">{{ total_suppliers }}</div>
                <div class="stat-label">Fournisseurs</div>
                <div class="mt-3">
                    <span class="badge bg-primary">Actifs: {{ total_suppliers }}</span>
                </div>
            </div>
            <div class="stat-card">
                <i class="stat-icon bi bi-currency-euro text-warning"></i>
                <div class="stat-number">{{ total_revenue }} €</div>
                <div class="stat-label">Revenu Total</div>
                <div class="progress mt-3">
                    <div class="progress-bar bg-warning" role="progressbar" style="width: 65%" aria-valuenow="65" aria-valuemin="0" aria-valuemax="100"></div>
                </div>
                <small class="text-muted mt-2 d-block">Objectif: 100,000 €</small>
            </div>
        </div>

        <!-- Charts Section -->
        <div class="charts-section">
            <div class="row">
                <div class="col-lg-8 mb-4">
                    <div class="chart-container">
                        <h3>Commandes (6 derniers mois)</h3>
                        <div class="d-flex justify-content-end mb-3">
                            <div class="btn-group btn-group-sm" role="group">
                                <button type="button" class="btn btn-outline-secondary active">6 Mois</button>
                                <button type="button" class="btn btn-outline-secondary">1 An</button>
                            </div>
                        </div>
                        <canvas id="ordersChart"></canvas>
                    </div>
                </div>
                <div class="col-lg-4 mb-4">
                    <div class="chart-container">
                        <h3>Top 5 Produits Vendus</h3>
                        <canvas id="productsChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Low Stock & Recent Orders -->
        <div class="row">
            <div class="col-lg-6 mb-4">
                <div class="table-container">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h3><i class="bi bi-exclamation-triangle text-warning me-2"></i>Produits à Stock Faible</h3>
                        <a href="{% url 'product_list' %}?filter=low_stock" class="btn btn-sm btn-outline-primary">Voir Tous</a>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Produit</th>
                                    <th>Stock Actuel</th>
                                    <th>Stock Min</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for product in low_stock_products %}
                                <tr>
                                    <td>{{ product.name }}</td>
                                    <td>
                                        <span class="badge bg-danger">{{ product.quantity_in_stock }}</span>
                                    </td>
                                    <td>{{ product.min_stock_level }}</td>
                                    <td>
                                        <a href="{% url 'product_list' %}" class="btn btn-sm btn-outline-primary">
                                            <i class="bi bi-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="4" class="text-center py-3">Aucun produit à stock faible</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 mb-4">
                <div class="table-container">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h3><i class="bi bi-clock-history text-info me-2"></i>Commandes Récentes</h3>
                        <a href="{% url 'order_list' %}" class="btn btn-sm btn-outline-primary">Voir Toutes</a>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>Date</th>
                                    <th>Statut</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for order in recent_orders %}
                                <tr>
                                    <td>{{ order.id }}</td>
                                    <td>{{ order.date|date:"d/m/Y" }}</td>
                                    <td>
                                        {% if order.status == 'pending' %}
                                            <span class="badge bg-warning">{{ order.status|title }}</span>
                                        {% elif order.status == 'completed' %}
                                            <span class="badge bg-success">{{ order.status|title }}</span>
                                        {% else %}
                                            <span class="badge bg-secondary">{{ order.status|title }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <a href="{% url 'edit_order' order.id %}" class="btn btn-sm btn-outline-secondary">
                                            <i class="bi bi-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="4" class="text-center py-3">Aucune commande récente.</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer>
        <p>© 2025 Gestion des Commandes - Tous droits réservés.</p>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Dark Mode Toggle
        const html = document.documentElement;
        const toggleSwitch = document.getElementById('toggleSwitch');

        // Load dark mode preference
        if (localStorage.getItem('darkMode') === 'true') {
            html.classList.add('dark-mode');
            toggleSwitch.checked = true;
        }

        // Toggle dark mode
        toggleSwitch.addEventListener('change', () => {
            html.classList.toggle('dark-mode');
            localStorage.setItem('darkMode', html.classList.contains('dark-mode'));
        });

        // Initialize Charts
        document.addEventListener('DOMContentLoaded', function() {
            try {
                // Parse the data from Django context
                const ordersLabels = JSON.parse('{{ orders_labels|safe }}');
                const ordersValues = JSON.parse('{{ orders_values|safe }}');
                const topProductsLabels = JSON.parse('{{ top_products_labels|safe }}');
                const topProductsValues = JSON.parse('{{ top_products_values|safe }}');

                console.log('Orders Labels:', ordersLabels);
                console.log('Orders Values:', ordersValues);
                console.log('Top Products Labels:', topProductsLabels);
                console.log('Top Products Values:', topProductsValues);

                // Function to update chart colors based on dark mode
                function updateChartColors() {
                    const isDarkMode = document.documentElement.classList.contains('dark-mode');
                    const textColor = isDarkMode ? '#e0e0e0' : '#212529';

                    // Orders Chart
                    const ordersChart = new Chart(document.getElementById('ordersChart'), {
                        type: 'line',
                        data: {
                            labels: ordersLabels,
                            datasets: [{
                                label: 'Commandes',
                                data: ordersValues,
                                borderColor: '#007bff',
                                backgroundColor: 'rgba(0, 123, 255, 0.1)',
                                fill: true,
                                tension: 0.4
                            }]
                        },
                        options: {
                            responsive: true,
                            plugins: {
                                legend: {
                                    display: true,
                                    labels: {
                                        color: textColor
                                    }
                                },
                                tooltip: {
                                    mode: 'index',
                                    intersect: false
                                }
                            },
                            scales: {
                                x: {
                                    ticks: {
                                        color: textColor
                                    },
                                    grid: {
                                        color: isDarkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)'
                                    }
                                },
                                y: {
                                    beginAtZero: true,
                                    ticks: {
                                        color: textColor
                                    },
                                    grid: {
                                        color: isDarkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)'
                                    }
                                }
                            }
                        }
                    });

                    // Products Chart
                    const productsChart = new Chart(document.getElementById('productsChart'), {
                        type: 'bar',
                        data: {
                            labels: topProductsLabels,
                            datasets: [{
                                label: 'Quantité Vendue',
                                data: topProductsValues,
                                backgroundColor: [
                                    'rgba(255, 99, 132, 0.7)',
                                    'rgba(54, 162, 235, 0.7)',
                                    'rgba(255, 206, 86, 0.7)',
                                    'rgba(75, 192, 192, 0.7)',
                                    'rgba(153, 102, 255, 0.7)'
                                ],
                                borderColor: [
                                    'rgba(255, 99, 132, 1)',
                                    'rgba(54, 162, 235, 1)',
                                    'rgba(255, 206, 86, 1)',
                                    'rgba(75, 192, 192, 1)',
                                    'rgba(153, 102, 255, 1)'
                                ],
                                borderWidth: 1
                            }]
                        },
                        options: {
                            responsive: true,
                            plugins: {
                                legend: {
                                    display: true,
                                    labels: {
                                        color: textColor
                                    }
                                }
                            },
                            scales: {
                                x: {
                                    ticks: {
                                        color: textColor
                                    },
                                    grid: {
                                        color: isDarkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)'
                                    }
                                },
                                y: {
                                    beginAtZero: true,
                                    ticks: {
                                        color: textColor
                                    },
                                    grid: {
                                        color: isDarkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)'
                                    }
                                }
                            }
                        }
                    });

                    // Update charts when dark mode changes
                    toggleSwitch.addEventListener('change', function() {
                        // Destroy and recreate charts with new colors
                        ordersChart.destroy();
                        productsChart.destroy();
                        updateChartColors();
                    });
                }

                // Initialize charts
                updateChartColors();
            } catch (error) {
                console.error('Error initializing charts:', error);
                // Display a user-friendly error message
                document.getElementById('ordersChart').getContext('2d').clearRect(0, 0,
                    document.getElementById('ordersChart').width,
                    document.getElementById('ordersChart').height);
                document.getElementById('productsChart').getContext('2d').clearRect(0, 0,
                    document.getElementById('productsChart').width,
                    document.getElementById('productsChart').height);

                const errorMessage = document.createElement('div');
                errorMessage.className = 'alert alert-danger mt-3';
                errorMessage.textContent = 'Impossible de charger les graphiques. Veuillez rafraîchir la page.';

                document.getElementById('ordersChart').parentNode.appendChild(errorMessage.cloneNode(true));
                document.getElementById('productsChart').parentNode.appendChild(errorMessage.cloneNode(true));
            }
        });
    </script>
</body>
</html>
