Byte-compiled / optimized / DLL files

pycache/ *.py[cod] *$py.class

C extensions

*.so

Distribution / packaging

.Python build/ develop-eggs/ dist/ downloads/ eggs/ .eggs/ lib/ lib64/ parts/ sdist/ var/ wheels/ share/python-wheels/ *.egg-info/ .installed.cfg *.egg MANIFEST

PyInstaller

*.manifest *.spec

Installer logs

pip-log.txt pip-delete-this-directory.txt

Unit test / coverage reports

htmlcov/ .tox/ .nox/ .coverage .coverage.* .cache nosetests.xml coverage.xml *.cover *.py,cover .hypothesis/ .pytest_cache/ cover/

Translations

*.mo *.pot

Django stuff

*.log local_settings.py db.sqlite3

Flask stuff

instance/ .webassets-cache

Scrapy stuff

.scrapy

Sphinx documentation

docs/_build/ docs/_static/ docs/_templates/

PyBuilder

target/

Jupyter Notebook

.ipynb_checkpoints

IPython

profile_default/ ipython_config.py

pyenv

.python-version

celery beat schedule file

celerybeat-schedule

SageMath parsed files

*.sage.py

Environments

.env .venv env/ venv/ ENV/ env.bak/ venv.bak/

Spyder project settings

.spyderproject .spyproject

Rope project settings

.ropeproject

mkdocs documentation

/site

mypy

.mypy_cache/ .dmypy.json dmypy.json

Pyre type checker

.pyre/

pytype static type analyzer

.pytype/

Cython debug symbols

cython_debug/

# Project specific
upload_excel.xlsx
architecture.txt
log.txt
file_log.txt
script.txt

# Docker volumes and data
staticfiles/
media/
postgres_data/
grafana_data/
prometheus_data/
ollama_data/

# Temporary and script files
*.tmp
*.bak
fix-grafana-vm.sh
troubleshoot-grafana.sh
check-vm-services.sh