{"dashboard": {"id": null, "title": "SNDP Agile Django Application Dashboard", "tags": ["django", "sndp-agile"], "style": "dark", "timezone": "browser", "panels": [{"id": 1, "title": "HTTP Requests per Second", "type": "graph", "targets": [{"expr": "rate(django_http_requests_total[5m])", "legendFormat": "{{method}} {{handler}}"}], "xAxis": {"show": true}, "yAxes": [{"label": "requests/sec", "show": true}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}}, {"id": 2, "title": "HTTP Response Status Codes", "type": "graph", "targets": [{"expr": "rate(django_http_responses_total[5m])", "legendFormat": "{{status}}"}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}}, {"id": 3, "title": "Database Connections", "type": "singlestat", "targets": [{"expr": "django_db_connections_total", "legendFormat": "Connections"}], "gridPos": {"h": 4, "w": 6, "x": 0, "y": 8}}, {"id": 4, "title": "Active Users", "type": "singlestat", "targets": [{"expr": "django_http_requests_total", "legendFormat": "Total Requests"}], "gridPos": {"h": 4, "w": 6, "x": 6, "y": 8}}], "time": {"from": "now-1h", "to": "now"}, "refresh": "30s"}}