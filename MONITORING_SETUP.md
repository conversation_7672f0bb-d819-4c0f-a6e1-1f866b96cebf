# SNDP Agile Monitoring Setup

This setup includes Prometheus and Grafana monitoring for your Django application.

## 🚀 Quick Start

1. **Start all services with monitoring:**
   ```bash
   chmod +x start-monitoring.sh
   ./start-monitoring.sh
   ```

2. **Or manually start with Docker Compose:**
   ```bash
   docker-compose up --build -d
   ```

## 📊 Services & Access URLs

| Service | URL | Credentials |
|---------|-----|-------------|
| **SNDP Agile App** | http://localhost:8000 | Your app login |
| **Prometheus** | http://localhost:9090 | No auth |
| **Grafana** | http://localhost:3000 | aziz/aziz123 |
| **Django Metrics** | http://localhost:8000/metrics | No auth |

## 📈 What's Being Monitored

### Django Application Metrics:
- HTTP request rates and response times
- HTTP status code distribution
- Database connection pool usage
- Django model operations
- Cache hit/miss rates
- User authentication events

### System Metrics:
- Container resource usage (CPU, Memory)
- Database performance
- Ollama AI service status

## 🎯 Grafana Dashboards

The setup includes a pre-configured Django dashboard showing:
- Request rate per endpoint
- Response time percentiles
- Error rate by status code
- Database query performance
- Active user sessions

## 🔧 Configuration Files

```
monitoring/
├── prometheus.yml              # Prometheus configuration
├── grafana/
│   ├── datasources/
│   │   └── prometheus.yml     # Auto-configured Prometheus datasource
│   └── dashboards/
│       ├── dashboard.yml      # Dashboard provider config
│       └── django-dashboard.json  # Django metrics dashboard
```

## 📊 Key Prometheus Metrics

- `django_http_requests_total` - Total HTTP requests
- `django_http_responses_total` - HTTP responses by status
- `django_db_connections_total` - Database connections
- `django_cache_operations_total` - Cache operations
- `django_model_operations_total` - Model CRUD operations

## 🛠️ Customization

### Adding Custom Metrics:
1. Install django-prometheus: `pip install django-prometheus`
2. Add custom metrics in your views:
   ```python
   from django_prometheus.models import ExportModelOperationsMixin
   from prometheus_client import Counter, Histogram
   
   custom_counter = Counter('my_custom_metric', 'Description')
   custom_counter.inc()
   ```

### Adding New Dashboards:
1. Create dashboard in Grafana UI
2. Export JSON and save to `monitoring/grafana/dashboards/`
3. Restart Grafana container

## 🔍 Troubleshooting

### Check service status:
```bash
docker-compose ps
```

### View logs:
```bash
docker-compose logs -f web        # Django app
docker-compose logs -f prometheus # Prometheus
docker-compose logs -f grafana    # Grafana
```

### Restart specific service:
```bash
docker-compose restart prometheus
```

### Access metrics directly:
- Django metrics: `curl http://localhost:8000/metrics`
- Prometheus targets: http://localhost:9090/targets

## 🚫 Stopping Services

```bash
docker-compose down              # Stop all services
docker-compose down -v           # Stop and remove volumes
```

## 💡 Tips

1. **Memory Usage**: Monitoring adds ~1.5GB RAM usage
2. **Data Retention**: Prometheus keeps 15 days of data by default
3. **Performance**: Metrics collection has minimal impact on app performance
4. **Alerts**: Consider adding Alertmanager for production monitoring
