#!/bin/bash

# SNDP Agile Pipeline Setup Script
# Optimized for 20GB RAM VM with Django + Ollama containers running
#
# Memory Allocation Plan (Total: ~20GB):
# - SNDP Agile Django App: ~2GB
# - Ollama + Llama3.2:1b: ~3GB
# - SonarQube + PostgreSQL: ~4GB
# - Prometheus: ~1GB
# - Grafana: ~512MB
# - ELK Stack: ~6GB
# - Jenkins: ~2GB
# - System + Buffer: ~1.5GB
# Total: ~20GB

# Exit on any error
set -e

# Check if running as root
if [ "$(id -u)" != "0" ]; then
  echo "This script must be run as root" 1>&2
  exit 1
fi

# System requirements for SonarQube and Elasticsearch
echo "Setting up system requirements for 20GB RAM VM..."
echo "vm.max_map_count=262144" >> /etc/sysctl.conf
echo "fs.file-max=65536" >> /etc/sysctl.conf
echo "vm.swappiness=10" >> /etc/sysctl.conf
sysctl -p

# Install Docker and Docker Compose if not present
echo "Installing Docker and dependencies..."
if ! command -v docker &> /dev/null; then
  apt-get update
  apt-get install -y docker.io curl wget gnupg lsb-release
fi

# Install latest Docker Compose
if ! command -v docker-compose &> /dev/null; then
  curl -L "https://github.com/docker/compose/releases/download/v2.24.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
  chmod +x /usr/local/bin/docker-compose
fi

# Check Docker socket access
if [ ! -S /var/run/docker.sock ]; then
  echo "Docker socket not found at /var/run/docker.sock"
  exit 1
fi

# Enable and start Docker service
systemctl enable docker
systemctl start docker

# Add user to docker group
usermod -aG docker aziz1

# Configure DNS for Docker with optimized settings for 20GB RAM
mkdir -p /etc/docker
cat << EOF > /etc/docker/daemon.json
{
  "dns": ["8.8.8.8", "8.8.4.4"],
  "log-driver": "json-file",
  "log-opts": {
    "max-size": "10m",
    "max-file": "3"
  },
  "default-ulimits": {
    "nofile": {
      "Name": "nofile",
      "Hard": 65536,
      "Soft": 65536
    }
  }
}
EOF

# Reload Docker configuration
systemctl restart docker
sleep 10

# Create directories for Docker Compose files
mkdir -p /home/<USER>/sonarqube /home/<USER>/prometheus /home/<USER>/grafana /home/<USER>/elk /home/<USER>/jenkins
chown -R aziz1:aziz1 /home/<USER>
chmod -R 755 /home/<USER>

# SonarQube docker-compose.yml (Optimized for 20GB RAM)
cat << EOF > /home/<USER>/sonarqube/docker-compose.yml
version: '3.8'

services:
  postgres:
    image: postgres:15-alpine
    container_name: sonarqube_postgres
    environment:
      POSTGRES_USER: sonar
      POSTGRES_PASSWORD: sonar123
      POSTGRES_DB: sonarqube
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --lc-collate=C --lc-ctype=C"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U sonar -d sonarqube"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s
    networks:
      - sonarqube_net
    mem_limit: 1g
    mem_reservation: 512m
    cpus: "1.0"
    restart: unless-stopped

  sonarqube:
    image: sonarqube:10.6.0-community
    container_name: sonarqube_server
    ports:
      - "9000:9000"
    environment:
      SONAR_JDBC_URL: *****************************************
      SONAR_JDBC_USERNAME: sonar
      SONAR_JDBC_PASSWORD: sonar123
      SONAR_ES_BOOTSTRAP_CHECKS_DISABLE: "true"
      SONAR_WEB_JAVAOPTS: "-Xmx1g -Xms512m -XX:+UseG1GC"
      SONAR_CE_JAVAOPTS: "-Xmx1g -Xms512m -XX:+UseG1GC"
      SONAR_SEARCH_JAVAOPTS: "-Xmx512m -Xms256m -XX:+UseG1GC"
    volumes:
      - sonarqube_data:/opt/sonarqube/data
      - sonarqube_logs:/opt/sonarqube/logs
      - sonarqube_extensions:/opt/sonarqube/extensions
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - sonarqube_net
    mem_limit: 3g
    mem_reservation: 2g
    cpus: "1.0"
    restart: unless-stopped
    ulimits:
      nofile:
        soft: 65536
        hard: 65536
volumes:
  sonarqube_data:
  sonarqube_logs:
  sonarqube_extensions:
  postgres_data:
networks:
  sonarqube_net:
    driver: bridge
EOF
chown aziz1:aziz1 /home/<USER>/sonarqube/docker-compose.yml
chmod 644 /home/<USER>/sonarqube/docker-compose.yml

# Prometheus docker-compose.yml (Optimized for 20GB RAM)
cat << EOF > /home/<USER>/prometheus/docker-compose.yml
version: '3.8'

services:
  prometheus:
    image: prom/prometheus:v2.54.1
    container_name: prometheus_server
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=30d'
      - '--storage.tsdb.retention.size=10GB'
      - '--web.enable-lifecycle'
      - '--web.enable-admin-api'
    mem_limit: 1g
    mem_reservation: 512m
    cpus: "0.5"
    restart: unless-stopped

volumes:
  prometheus_data:
EOF
chown aziz1:aziz1 /home/<USER>/prometheus/docker-compose.yml
chmod 644 /home/<USER>/prometheus/docker-compose.yml

# Prometheus configuration (Updated for SNDP Agile monitoring)
cat << EOF > /home/<USER>/prometheus/prometheus.yml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  - job_name: 'sndp-agile-django'
    static_configs:
      - targets: ['host.docker.internal:8000']
    metrics_path: '/metrics'
    scrape_interval: 30s
    scrape_timeout: 10s

  - job_name: 'sonarqube'
    static_configs:
      - targets: ['host.docker.internal:9000']
    metrics_path: '/api/monitoring/metrics'
    scrape_interval: 60s
    scrape_timeout: 30s

  - job_name: 'grafana'
    static_configs:
      - targets: ['host.docker.internal:3000']
    metrics_path: '/metrics'
    scrape_interval: 60s

  - job_name: 'jenkins'
    static_configs:
      - targets: ['host.docker.internal:8080']
    metrics_path: '/prometheus'
    scrape_interval: 60s

  - job_name: 'node-exporter'
    static_configs:
      - targets: ['host.docker.internal:9100']
    scrape_interval: 30s

  - job_name: 'docker'
    static_configs:
      - targets: ['host.docker.internal:9323']
    scrape_interval: 30s
EOF
chown aziz1:aziz1 /home/<USER>/prometheus/prometheus.yml
chmod 644 /home/<USER>/prometheus/prometheus.yml

# Grafana docker-compose.yml (Optimized for 20GB RAM)
cat << EOF > /home/<USER>/grafana/docker-compose.yml
version: '3.8'

services:
  grafana:
    image: grafana/grafana:11.2.0
    container_name: grafana_server
    ports:
      - "3000:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./grafana.ini:/etc/grafana/grafana.ini:ro
      - ./dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./datasources:/etc/grafana/provisioning/datasources:ro
    environment:
      GF_SECURITY_ADMIN_USER: aziz
      GF_SECURITY_ADMIN_PASSWORD: aziz123
      GF_INSTALL_PLUGINS: grafana-piechart-panel,grafana-worldmap-panel,grafana-clock-panel
      GF_RENDERING_SERVER_URL: http://renderer:8081/render
      GF_RENDERING_CALLBACK_URL: http://grafana:3000/
      GF_LOG_FILTERS: rendering:debug
    mem_limit: 512m
    mem_reservation: 256m
    cpus: "0.3"
    restart: unless-stopped

volumes:
  grafana_data:
EOF
chown aziz1:aziz1 /home/<USER>/grafana/docker-compose.yml
chmod 644 /home/<USER>/grafana/docker-compose.yml

# ELK docker-compose.yml (Optimized for 20GB RAM)
cat << EOF > /home/<USER>/elk/docker-compose.yml
version: '3.8'

services:
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.15.1
    container_name: elasticsearch_server
    ports:
      - "9200:9200"
      - "9300:9300"
    environment:
      discovery.type: single-node
      xpack.security.enabled: "false"
      xpack.monitoring.collection.enabled: "true"
      ES_JAVA_OPTS: "-Xms4g -Xmx4g"
      bootstrap.memory_lock: "true"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    ulimits:
      memlock:
        soft: -1
        hard: -1
      nofile:
        soft: 65536
        hard: 65536
    mem_limit: 6g
    mem_reservation: 4g
    cpus: "2.0"
    restart: unless-stopped
  logstash:
    image: docker.elastic.co/logstash/logstash:8.15.1
    ports:
      - "5044:5044"
    volumes:
      - ./logstash.conf:/usr/share/logstash/pipeline/logstash.conf
    depends_on:
      - elasticsearch
    mem_limit: 256m
    mem_reservation: 128m
    cpus: "0.2"
    restart: always
  kibana:
    image: docker.elastic.co/kibana/kibana:8.15.1
    ports:
      - "5601:5601"
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    depends_on:
      - elasticsearch
    mem_limit: 256m
    mem_reservation: 128m
    cpus: "0.2"
    restart: always
  filebeat:
    image: docker.elastic.co/beats/filebeat:8.15.1
    volumes:
      - ./filebeat.yml:/usr/share/filebeat/filebeat.yml
      - /var/log:/var/log:ro
    depends_on:
      - logstash
    mem_limit: 256m
    mem_reservation: 128m
    cpus: "0.2"
    restart: always
volumes:
  elasticsearch_data:
EOF
chown aziz1:aziz1 /home/<USER>/elk/docker-compose.yml
chmod 644 /home/<USER>/elk/docker-compose.yml

# Logstash configuration
cat << EOF > /home/<USER>/elk/logstash.conf
input {
  beats {
    port => 5044
  }
}
output {
  elasticsearch {
    hosts => ["http://elasticsearch:9200"]
    index => "agil-app-%{+YYYY.MM.dd}"
  }
}
EOF
chown aziz1:aziz1 /home/<USER>/elk/logstash.conf
chmod 644 /home/<USER>/elk/logstash.conf

# Filebeat configuration
cat << EOF > /home/<USER>/elk/filebeat.yml
filebeat.inputs:
  - type: kubernetes
    enabled: true
    paths:
      - /var/log/containers/*.log
    processors:
      - add_kubernetes_metadata:
          host: \${NODE_NAME}
          matchers:
          - logs_path:
              logs_path: "/var/log/containers/"
output.logstash:
  hosts: ["logstash:5044"]
EOF
chown aziz1:aziz1 /home/<USER>/elk/filebeat.yml
chmod 644 /home/<USER>/elk/filebeat.yml

# Jenkins docker-compose.yml
cat << EOF > /home/<USER>/jenkins/docker-compose.yml
services:
  jenkins:
    image: jenkins/jenkins:lts-jdk17
    user: root
    command: >
      sh -c "apt-get update && apt-get install -y docker.io && usermod -aG docker jenkins && su jenkins -c 'java -jar /usr/share/jenkins/jenkins.war'"
    ports:
      - "8080:8080"
      - "50000:50000"
    volumes:
      - jenkins_home:/var/jenkins_home
      - ./init.groovy.d:/var/jenkins_home/init.groovy.d
      - /var/run/docker.sock:/var/run/docker.sock
    environment:
      - JAVA_OPTS=-Djenkins.install.runSetupWizard=false -Xmx512m -Xms256m -Dblueocean.enabled=true
    mem_limit: 1g
    mem_reservation: 512m
    cpus: "0.6"
    restart: always
volumes:
  jenkins_home:
EOF
chown aziz1:aziz1 /home/<USER>/jenkins/docker-compose.yml
chmod 644 /home/<USER>/jenkins/docker-compose.yml

# Jenkins init.groovy.d directory and script
mkdir -p /home/<USER>/jenkins/init.groovy.d
chown aziz1:aziz1 /home/<USER>/jenkins/init.groovy.d
chmod 755 /home/<USER>/jenkins/init.groovy.d

cat << EOF > /home/<USER>/jenkins/init.groovy.d/init.groovy
import jenkins.model.*
import hudson.security.*

def instance = Jenkins.getInstance()
def hudsonRealm = new HudsonPrivateSecurityRealm(false)
hudsonRealm.createAccount("aziz", "aziz")
instance.setSecurityRealm(hudsonRealm)
def strategy = new FullControlOnceLoggedInAuthorizationStrategy()
strategy.setAllowAnonymousRead(false)
instance.setAuthorizationStrategy(strategy)
instance.save()
EOF
chown aziz1:aziz1 /home/<USER>/jenkins/init.groovy.d/init.groovy
chmod 644 /home/<USER>/jenkins/init.groovy.d/init.groovy

# Create systemd service for Docker Compose autostart
cat << EOF > /etc/systemd/system/docker-compose-autostart.service
[Unit]
Description=Docker Compose Services
Requires=docker.service
After=docker.service

[Service]
Type=oneshot
RemainAfterExit=yes
ExecStart=/usr/bin/docker-compose -f /home/<USER>/sonarqube/docker-compose.yml up -d
ExecStart=/usr/bin/docker-compose -f /home/<USER>/prometheus/docker-compose.yml up -d
ExecStart=/usr/bin/docker-compose -f /home/<USER>/grafana/docker-compose.yml up -d
ExecStart=/usr/bin/docker-compose -f /home/<USER>/elk/docker-compose.yml up -d
ExecStart=/usr/bin/docker-compose -f /home/<USER>/jenkins/docker-compose.yml up -d
ExecStop=/usr/bin/docker-compose -f /home/<USER>/sonarqube/docker-compose.yml down
ExecStop=/usr/bin/docker-compose -f /home/<USER>/prometheus/docker-compose.yml down
ExecStop=/usr/bin/docker-compose -f /home/<USER>/grafana/docker-compose.yml down
ExecStop=/usr/bin/docker-compose -f /home/<USER>/elk/docker-compose.yml down
ExecStop=/usr/bin/docker-compose -f /home/<USER>/jenkins/docker-compose.yml down
User=aziz1
WorkingDirectory=/home/<USER>

[Install]
WantedBy=multi-user.target
EOF
chmod 644 /etc/systemd/system/docker-compose-autostart.service

# Enable and start systemd service
systemctl daemon-reload
systemctl enable docker-compose-autostart
systemctl start docker-compose-autostart

# Pull Docker images
docker pull sonarqube:10.6.0-community || true
docker pull postgres:15 || true
docker pull prom/prometheus:v2.54.1 || true
docker pull grafana/grafana:11.2.0 || true
docker pull docker.elastic.co/elasticsearch/elasticsearch:8.15.1 || true
docker pull docker.elastic.co/logstash/logstash:8.15.1 || true
docker pull docker.elastic.co/kibana/kibana:8.15.1 || true
docker pull docker.elastic.co/beats/filebeat:8.15.1 || true
docker pull jenkins/jenkins:lts-jdk17 || true

# Create startup script for pipeline services
cat << EOF > /home/<USER>/start-pipeline.sh
#!/bin/bash

echo "Starting SNDP Agile Pipeline Services..."
echo "Note: Make sure your Django app with Ollama is not running to avoid memory conflicts"
echo ""

# Check available memory
AVAILABLE_MEM=\$(free -m | awk 'NR==2{printf "%.0f", \$7}')
echo "Available memory: \${AVAILABLE_MEM}MB"

if [ "\$AVAILABLE_MEM" -lt 8192 ]; then
    echo "Warning: Less than 8GB available memory. Consider stopping other services."
    read -p "Continue anyway? (y/N): " -n 1 -r
    echo
    if [[ ! \$REPLY =~ ^[Yy]\$ ]]; then
        exit 1
    fi
fi

# Start services in order
echo "Starting SonarQube..."
cd /home/<USER>/sonarqube && docker-compose up -d

echo "Starting Prometheus..."
cd /home/<USER>/prometheus && docker-compose up -d

echo "Starting Grafana..."
cd /home/<USER>/grafana && docker-compose up -d

echo "Starting ELK Stack..."
cd /home/<USER>/elk && docker-compose up -d

echo "Starting Jenkins..."
cd /home/<USER>/jenkins && docker-compose up -d

echo ""
echo "Pipeline services started! Access URLs:"
echo "  SonarQube: http://localhost:9000 (sonar/sonar123)"
echo "  Prometheus: http://localhost:9090"
echo "  Grafana: http://localhost:3000 (aziz/aziz123)"
echo "  Kibana: http://localhost:5601"
echo "  Jenkins: http://localhost:8080 (aziz/aziz)"
echo ""
echo "To stop all services: /home/<USER>/stop-pipeline.sh"
EOF

# Create stop script
cat << EOF > /home/<USER>/stop-pipeline.sh
#!/bin/bash

echo "Stopping SNDP Agile Pipeline Services..."

for dir in jenkins elk grafana prometheus sonarqube; do
    echo "Stopping \$dir..."
    cd /home/<USER>/\$dir && docker-compose down
done

echo "All pipeline services stopped."
EOF

# Make scripts executable
chmod +x /home/<USER>/start-pipeline.sh
chmod +x /home/<USER>/stop-pipeline.sh
chown aziz1:aziz1 /home/<USER>/start-pipeline.sh
chown aziz1:aziz1 /home/<USER>/stop-pipeline.sh

echo ""
echo "=== SNDP Agile Pipeline Setup Complete ==="
echo ""
echo "Memory-optimized for 20GB RAM with Django + Ollama running"
echo ""
echo "To start pipeline services:"
echo "  sudo -u aziz1 /home/<USER>/start-pipeline.sh"
echo ""
echo "To stop pipeline services:"
echo "  sudo -u aziz1 /home/<USER>/stop-pipeline.sh"
echo ""
echo "Service URLs (after starting):"
echo "  SonarQube: http://localhost:9000 (sonar/sonar123)"
echo "  Prometheus: http://localhost:9090"
echo "  Grafana: http://localhost:3000 (aziz/aziz123)"
echo "  Kibana: http://localhost:5601"
echo "  Jenkins: http://localhost:8080 (aziz/aziz)"