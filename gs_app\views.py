import json
import logging
import requests
import pandas as pd
import os
from datetime import datetime, timedelta
from django.utils.timezone import now
from django.db.models import Sum
from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.contrib import messages
from django.core.mail import send_mail
from django.conf import settings

from .models import Product, Supplier, Order, OrderDetail
from .forms import ProductForm, SupplierForm, OrderForm, OrderDetailForm
from .utils import translate_fr_to_en, prepare_prompt_from_df

logger = logging.getLogger(__name__)

# Get Ollama host from environment variable or default to localhost
OLLAMA_HOST = os.getenv('OLLAMA_HOST', 'http://localhost:11434')


@login_required
def home(request):
    return render(request, 'home.html')

# Ajouter un produit
def add_product(request):
    if request.method == 'POST':
        form = ProductForm(request.POST)
        if form.is_valid():
            product = form.save()

            # Check if restock was requested
            restock = request.POST.get('restock', 'no') == 'yes'

            if restock:
                # Send email notification about restocking
                subject = f'SNDP Agile: Nouveau produit ajouté - {product.name}'
                message = f"""
Bonjour,

Nous vous informons qu'un nouveau produit a été ajouté à l'inventaire et réapprovisionné:

DÉTAILS DU PRODUIT:
------------------------------------------
Nom du produit: {product.name}
Catégorie: {product.category}
Fournisseur: {product.supplier}
Quantité en stock: {product.quantity_in_stock} unités
Prix unitaire: {product.price}€
------------------------------------------

Cette opération a été effectuée le {datetime.now().strftime('%d/%m/%Y à %H:%M')}.

Pour plus de détails, veuillez vous connecter au système de gestion de stock SNDP Agile.

Cordialement,
L'équipe SNDP Agile
<EMAIL>
+216 123 456 789

------------------------------------------
Ce message est généré automatiquement. Merci de ne pas y répondre.
                """

                try:
                    send_mail(
                        subject=subject,
                        message=message,
                        from_email=settings.DEFAULT_FROM_EMAIL,
                        recipient_list=['<EMAIL>'],
                        fail_silently=False,
                    )
                    messages.success(request, f'Produit ajouté et notification envoyée.')
                except Exception as e:
                    messages.error(request, f'Produit ajouté mais erreur d\'envoi d\'email: {str(e)}')
            else:
                messages.success(request, 'Produit ajouté avec succès.')

            return redirect('product_list')
    else:
        form = ProductForm()

    # If format=form is in the query params, return only the form HTML
    if request.GET.get('format') == 'form':
        return render(request, 'products/product_form_fields.html', {'form': form})

    return render(request, 'products/product_form.html', {'form': form})

# Modifier un produit

# Supprimer un produit
def delete_product(request, product_id):
    product = get_object_or_404(Product, id=product_id)
    if request.method == "POST":
        product.delete()
        return redirect('product_list')
    return render(request, 'products/product_confirm_delete.html', {'product': product})

# Liste des produits
def product_list(request):
    products = Product.objects.all()
    return render(request, 'products/product_list.html', {'products': products})

# Ajouter un fournisseur
def add_supplier(request):
    if request.method == 'POST':
        form = SupplierForm(request.POST)
        if form.is_valid():
            form.save()
            if request.headers.get('x-requested-with') == 'XMLHttpRequest':
                return JsonResponse({"message": "Fournisseur ajouté avec succès!"}, status=201)
            return redirect('supplier_list')
    else:
        form = SupplierForm()

    if request.GET.get('format') == 'form':
        # Return only the form for AJAX requests
        return render(request, 'suppliers/supplier_form_fields.html', {'form': form})

    return render(request, 'suppliers/add_supplier.html', {'form': form})

# Liste des fournisseurs
def supplier_list(request):
    suppliers = Supplier.objects.all()  # Récupère tous les fournisseurs
    return render(request, 'suppliers/supplier_list.html', {'suppliers': suppliers})

# Détails d'un fournisseur
def supplier_detail(request, id):
    supplier = get_object_or_404(Supplier, id=id)
    data = {
        "id": supplier.id,
        "name": supplier.name,
        "contact": supplier.contact
    }
    return JsonResponse(data)

def edit_supplier(request, id):
    supplier = get_object_or_404(Supplier, id=id)  # Get the supplier object

    if request.method == 'POST':
        form = SupplierForm(request.POST, instance=supplier)  # Bind the form to the existing supplier instance
        if form.is_valid():
            form.save()  # Save the updated supplier data
            return redirect('supplier_list')  # Redirect to the supplier list after successful update
    else:
        form = SupplierForm(instance=supplier)  # Pre-populate the form with the existing supplier data

    return render(request, 'suppliers/edit_supplier.html', {'form': form})


# Supprimer un fournisseur
def delete_supplier(request, id):
    supplier = get_object_or_404(Supplier, id=id)

    # If it's a POST request, delete the supplier
    if request.method == 'POST':
        supplier.delete()
        return redirect('supplier_list')  # Redirect to the supplier list after deletion

    # If it's a GET request, show the confirmation page
    return render(request, 'suppliers/delete_supplier.html', {'supplier': supplier})



# Liste des commandes
def order_list(request):
    orders = Order.objects.all().order_by('-date')
    return render(request, 'orders/order_list.html', {'orders': orders})

# Ajouter une commande
def add_order(request):
    products = Product.objects.all()

    if request.method == "POST":
        form = OrderForm(request.POST)
        if form.is_valid():
            order = form.save(commit=False)

            # Get the status
            status = form.cleaned_data['status']

            # Save the order
            order.save()

            # Process selected products and quantities
            selected_products = form.cleaned_data['products']
            quantities_data = request.POST.get('quantities', '')

            if quantities_data:
                quantities = {}
                for item in quantities_data.split(','):
                    if ':' in item:
                        product_id, quantity = item.split(':')
                        quantities[product_id] = int(quantity)

                # Create OrderDetail for each selected product
                for product in selected_products:
                    quantity = quantities.get(str(product.id), 1)
                    OrderDetail.objects.create(
                        order=order,
                        product=product,
                        quantity=quantity,
                        price_at_order=product.price
                    )

                    # If order is completed, update product quantities immediately
                    if status == 'completed':
                        product.quantity_in_stock += quantity
                        product.save()

            if status == 'completed':
                messages.success(request, 'Commande ajoutée comme terminée et stocks mis à jour.')
            else:
                messages.success(request, 'Commande ajoutée avec succès.')

            return redirect('order_list')
        else:
            messages.error(request, 'Erreur lors de l\'ajout de la commande.')
    else:
        form = OrderForm()

    # If format=form is in the query params, return only the form HTML
    if request.GET.get('format') == 'form':
        return render(request, 'orders/order_form_fields.html', {
            'form': form,
            'products': products
        })

    return render(request, 'orders/add_order.html', {
        'form': form,
        'products': products
    })

# Modifier une commande
def edit_order(request, order_id):
    order = get_object_or_404(Order, id=order_id)
    order_details = OrderDetail.objects.filter(order=order)
    previous_status = order.status

    if request.method == "POST":
        form = OrderForm(request.POST, instance=order)
        if form.is_valid():
            # Get the new status before saving
            new_status = form.cleaned_data['status']

            logger.info(f"Editing order #{order.id}: Status change from '{previous_status}' to '{new_status}'")

            # Check if status is changing from 'pending' to 'completed'
            if previous_status == 'pending' and new_status == 'completed':
                # Update product quantities before saving the form
                for detail in order_details:
                    product = detail.product
                    old_quantity = product.quantity_in_stock
                    product.quantity_in_stock += detail.quantity
                    logger.info(f"Updating product #{product.id} ({product.name}) quantity: {old_quantity} + {detail.quantity} = {product.quantity_in_stock}")
                    product.save()

                # Now save the form with the new status
                updated_order = form.save()
                messages.success(request, f'Commande #{order.id} terminée et stocks mis à jour.')
            else:
                # Just save the form without updating quantities
                updated_order = form.save()
                messages.success(request, f'Commande #{order.id} mise à jour avec succès.')

            # Otherwise, redirect to the order list
            return redirect('order_list')
        else:
            logger.error(f"Form errors: {form.errors}")
            messages.error(request, 'Erreur lors de la mise à jour de la commande.')

            # If it's an AJAX request, return a JSON response with errors
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return JsonResponse({'success': False, 'errors': form.errors})
    else:
        form = OrderForm(instance=order)

    # If format=form is in the query params, return only the form HTML
    if request.GET.get('format') == 'form':
        return render(request, 'orders/order_edit_form_fields.html', {
            'form': form,
            'order': order,
            'order_details': order_details
        })

    # For non-AJAX requests, redirect to order detail list
    return redirect('order_detail_list', order_id=order.id)

# Supprimer une commande
def delete_order(request, order_id):
    order = get_object_or_404(Order, id=order_id)
    if request.method == "POST":
        order.delete()
        return redirect('order_list')
    return render(request, 'orders/order_confirm_delete.html', {'order': order})

# Liste des détails d'une commande (une seule fonction)
def order_detail_list(request, order_id):
    order = get_object_or_404(Order, id=order_id)
    details = order.order_details.all()  # Assure-toi que l'instance `order_details` est définie correctement dans ton modèle `Order`
    return render(request, 'orders/order_detail_list.html', {'order': order, 'details': details})

# Ajouter un détail de commande
def add_order_detail(request, order_id):
    order = get_object_or_404(Order, id=order_id)
    if request.method == "POST":
        form = OrderDetailForm(request.POST)
        if form.is_valid():
            order_detail = form.save(commit=False)
            order_detail.order = order
            order_detail.save()
            return redirect('order_detail_list', order_id=order.id)  # Redirection vers les détails de la commande
        else:
            return render(request, 'orders/order_detail_form.html', {'form': form, 'order': order, 'error': 'Le formulaire est invalide.'})
    else:
        form = OrderDetailForm()
    return render(request, 'orders/order_detail_form.html', {'form': form, 'order': order})

# Modifier un détail de commande
def edit_order_detail(request, order_detail_id):
    order_detail = get_object_or_404(OrderDetail, id=order_detail_id)
    if request.method == "POST":
        form = OrderDetailForm(request.POST, instance=order_detail)
        if form.is_valid():
            form.save()
            return redirect('order_detail_list', order_id=order_detail.order.id)  # Redirection vers les détails de la commande
        else:
            return render(request, 'orders/order_detail_form.html', {'form': form, 'error': 'Le formulaire est invalide.'})
    else:
        form = OrderDetailForm(instance=order_detail)
    return render(request, 'orders/order_detail_form.html', {'form': form})

# Supprimer un détail de commande
def delete_order_detail(request, order_detail_id):
    order_detail = get_object_or_404(OrderDetail, id=order_detail_id)
    if request.method == "POST":
        order_detail.delete()
        return redirect('order_detail_list', order_id=order_detail.order.id)  # Redirection vers les détails de la commande
    return render(request, 'orders/order_detail_confirm_delete.html', {'order_detail': order_detail})

from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt

@csrf_exempt  # Disable CSRF for simplicity (enable in production)
def ollama_chat(request):
    if request.method == "POST":
        try:
            # Handle both form data and JSON data
            if request.content_type == 'application/json':
                import json
                data = json.loads(request.body)
                user_message = data.get("message")
            else:
                user_message = request.POST.get("message")

            if not user_message:
                return JsonResponse({"error": "No message provided"}, status=400)

            # First, check if Ollama is running
            try:
                health_response = requests.get(f"{OLLAMA_HOST}/api/tags", timeout=10)
                health_response.raise_for_status()
            except requests.RequestException:
                return JsonResponse({
                    "error": "Ollama server is not running. Please start Ollama first."
                }, status=503)

            # Make the chat request
            response = requests.post(
                f"{OLLAMA_HOST}/api/chat",
                json={
                    "model": "llama3",  # Adjust model name if needed
                    "messages": [{"role": "user", "content": user_message}],
                    "stream": False
                },
                timeout=60
            )
            response.raise_for_status()
            data = response.json()
            bot_reply = data.get("message", {}).get("content", "No reply received.")
            return JsonResponse({"message": {"content": bot_reply}})

        except requests.Timeout:
            return JsonResponse({"error": "Request timed out. Ollama might be busy."}, status=504)
        except requests.RequestException as e:
            logger.error(f"Ollama request failed: {str(e)}")
            return JsonResponse({"error": f"Request failed: {str(e)}"}, status=500)
        except ValueError as e:
            logger.error(f"JSON parsing error: {str(e)}")
            return JsonResponse({"error": "Invalid JSON response from Ollama"}, status=500)
        except Exception as e:
            logger.error(f"Unexpected error in ollama_chat: {str(e)}")
            return JsonResponse({"error": f"Unexpected error: {str(e)}"}, status=500)

    return JsonResponse({"error": "Invalid request method"}, status=405)


def ask_ollama(prompt):
    """
    Alternative method to call Ollama using HTTP API instead of subprocess
    This is more reliable and doesn't depend on hardcoded paths
    """
    try:
        # First, check if Ollama is running
        try:
            health_response = requests.get(f"{OLLAMA_HOST}/api/tags", timeout=10)
            health_response.raise_for_status()
        except requests.RequestException:
            return "Error: Ollama server is not running. Please start Ollama first."

        # Make the chat request
        response = requests.post(
            f"{OLLAMA_HOST}/api/chat",
            json={
                "model": "llama3",
                "messages": [{"role": "user", "content": prompt}],
                "stream": False
            },
            timeout=120  # Allow more time for file analysis
        )
        response.raise_for_status()
        data = response.json()
        return data.get("message", {}).get("content", "No reply received.")

    except requests.Timeout:
        return "Error: Request timed out. Ollama might be busy processing."
    except requests.RequestException as e:
        logger.error(f"Ollama request failed in ask_ollama: {str(e)}")
        return f"Error: Request failed - {str(e)}"
    except ValueError as e:
        logger.error(f"JSON parsing error in ask_ollama: {str(e)}")
        return "Error: Invalid response from Ollama"
    except Exception as e:
        logger.error(f"Unexpected error in ask_ollama: {str(e)}")
        return f"Error: Unexpected error - {str(e)}"


def upload_excel(request):
    advice = ""
    if request.method == 'POST' and request.FILES.get('excel_file'):
        excel_file = request.FILES['excel_file']
        df = pd.read_excel(excel_file)
        prompt = prepare_prompt_from_df(df)
        advice = ask_ollama(prompt)

    return render(request, 'upload.html', {'advice': advice})




def edit_product(request, product_id):
    product = get_object_or_404(Product, id=product_id)

    if request.method == 'POST':
        form = ProductForm(request.POST, instance=product)
        if form.is_valid():
            # Get restock value from form
            restock = request.POST.get('restock', 'no') == 'yes'

            # Save the form
            product = form.save()

            # Handle restock logic if needed
            if restock:
                # Send email notification about restocking
                subject = f'SNDP Agile: Réapprovisionnement de produit - {product.name}'
                message = f"""
Bonjour,

Nous vous informons qu'un produit a été réapprovisionné:

DÉTAILS DU PRODUIT:
------------------------------------------
Nom du produit: {product.name}
Catégorie: {product.category}
Fournisseur: {product.supplier}
Quantité en stock: {product.quantity_in_stock} unités
Prix unitaire: {product.price}€
------------------------------------------

Cette opération a été effectuée le {datetime.now().strftime('%d/%m/%Y à %H:%M')}.

Pour plus de détails, veuillez vous connecter au système de gestion de stock SNDP Agile.

Cordialement,
L'équipe SNDP Agile
<EMAIL>
+216 123 456 789

------------------------------------------
Ce message est généré automatiquement. Merci de ne pas y répondre.
                """

                try:
                    send_mail(
                        subject=subject,
                        message=message,
                        from_email=settings.DEFAULT_FROM_EMAIL,
                        recipient_list=['<EMAIL>'],
                        fail_silently=False,
                    )
                    messages.success(request, f'Produit mis à jour et notification envoyée.')
                except Exception as e:
                    messages.error(request, f'Produit mis à jour mais erreur d\'envoi d\'email: {str(e)}')
            else:
                messages.success(request, 'Produit mis à jour avec succès.')

            return redirect('product_list')
    else:
        form = ProductForm(instance=product)

    # Check if this is an AJAX request for the form only
    if request.GET.get('format') == 'form':
        return render(request, 'products/product_form_fields.html', {
            'form': form,
            'product': product,
            'needs_restock': product.quantity_in_stock < 100
        })

    # Regular request - return full page
    return render(request, 'products/product_form.html', {
        'form': form,
        'product': product
    })


def dashboard(request):
    total_products = Product.objects.count()
    products_in_stock = Product.objects.filter(quantity_in_stock__gt=0).count()
    out_of_stock = Product.objects.filter(quantity_in_stock=0).count()
    total_suppliers = Supplier.objects.count()
    total_orders = Order.objects.count()
    total_revenue = OrderDetail.objects.aggregate(revenue=Sum('price_at_order'))['revenue'] or 0

    # Orders per month (last 6 months)
    today = now()
    six_months_ago = today - timedelta(days=180)
    orders = Order.objects.filter(date__gte=six_months_ago)

    orders_by_month = {}
    for i in range(6):
        month = (today - timedelta(days=i*30)).replace(day=1)
        key = month.strftime("%B %Y")
        orders_by_month[key] = 0

    for order in orders:
        key = order.date.strftime("%B %Y")
        if key in orders_by_month:
            orders_by_month[key] += 1
    orders_labels = list(reversed(list(orders_by_month.keys())))
    orders_values = list(reversed(list(orders_by_month.values())))

    # Top 5 products sold
    top_products_query = OrderDetail.objects.values('product__name').annotate(
        total_sold=Sum('quantity')).order_by('-total_sold')[:5]
    top_products_labels = [item['product__name'] for item in top_products_query]
    top_products_values = [item['total_sold'] for item in top_products_query]

    context = {
        'total_products': total_products,
        'products_in_stock': products_in_stock,
        'out_of_stock': out_of_stock,
        'total_suppliers': total_suppliers,
        'total_orders': total_orders,
        'total_revenue': total_revenue,

        'orders_labels': json.dumps(orders_labels),
        'orders_values': json.dumps(orders_values),
        'top_products_labels': json.dumps(top_products_labels),
        'top_products_values': json.dumps(top_products_values),

        'low_stock_products': Product.objects.filter(quantity_in_stock__lt=5),
        'recent_orders': Order.objects.order_by('-date')[:5],
    }

    return render(request, 'dashboard.html', context)

def map_view(request):
    return render(request, 'map.html')




def translate_text(request):
    if request.method == "POST":
        text = request.POST.get("text", "")
        translated = translate_fr_to_en(text)
        return JsonResponse({"translated_text": translated})




