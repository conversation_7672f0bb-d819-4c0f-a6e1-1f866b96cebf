#!/bin/bash

echo "🔍 Grafana Troubleshooting Script for VM Environment"
echo "=================================================="

# Function to check service status
check_service() {
    local service_name=$1
    local url=$2
    local expected_response=$3
    
    echo -n "Checking $service_name... "
    if curl -s "$url" | grep -q "$expected_response" 2>/dev/null; then
        echo "✅ OK"
        return 0
    else
        echo "❌ FAILED"
        return 1
    fi
}

# Check Docker containers
echo "📦 Docker Container Status:"
docker-compose ps

echo ""
echo "🌐 Network Connectivity Tests:"

# Test Prometheus
check_service "Prometheus" "http://localhost:9090/-/ready" "ready"

# Test Django app
check_service "Django App" "http://localhost:8000" "<!DOCTYPE html"

# Test Django metrics
check_service "Django Metrics" "http://localhost:8000/metrics" "django_"

# Test Grafana
check_service "Grafana" "http://localhost:3000/api/health" "ok"

echo ""
echo "🔧 Prometheus Configuration Check:"
echo "Prometheus targets status:"
curl -s http://localhost:9090/api/v1/targets | python3 -m json.tool 2>/dev/null || echo "Could not parse Prometheus targets"

echo ""
echo "📊 Available Metrics Check:"
echo "Checking for Django metrics in Prometheus:"
curl -s "http://localhost:9090/api/v1/label/__name__/values" | grep -o '"django_[^"]*"' | head -10

echo ""
echo "🗂️ File System Check:"
echo "Checking Grafana provisioning files:"
ls -la monitoring/grafana/datasources/
ls -la monitoring/grafana/dashboards/

echo ""
echo "📋 Grafana Logs (last 20 lines):"
docker-compose logs --tail=20 grafana

echo ""
echo "📋 Prometheus Logs (last 10 lines):"
docker-compose logs --tail=10 prometheus

echo ""
echo "🎯 Quick Fixes:"
echo "1. If Prometheus targets are down:"
echo "   docker-compose restart prometheus"
echo ""
echo "2. If Django metrics are missing:"
echo "   docker-compose restart web"
echo ""
echo "3. If Grafana dashboards are missing:"
echo "   docker-compose restart grafana"
echo ""
echo "4. To reset everything:"
echo "   docker-compose down && docker-compose up -d"
echo ""
echo "5. To check specific service logs:"
echo "   docker-compose logs -f [service_name]"
