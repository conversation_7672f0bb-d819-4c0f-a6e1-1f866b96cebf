/* Orders Page Professional Design with Black & Yellow Theme */
:root {
    --primary-color: #000000;
    --secondary-color: #ffff00;
    --accent-color: #ffd700;
    --dark-color: #1f1f1f;
    --light-color: #f8fafc;
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --gradient-primary: linear-gradient(135deg, #000000 0%, #333333 100%);
    --gradient-secondary: linear-gradient(135deg, #ffff00 0%, #ffd700 100%);
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, '<PERSON><PERSON>e UI', <PERSON><PERSON>, sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--light-color);
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    transition: all 0.3s ease;
}

/* Navigation */
.navbar {
    background: var(--gradient-primary) !important;
    backdrop-filter: blur(10px);
    box-shadow: var(--shadow-md);
    padding: 1rem 0;
    transition: all 0.3s ease;
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
    color: var(--secondary-color) !important;
    text-decoration: none;
}

.nav-link {
    color: rgba(255, 255, 255, 0.9) !important;
    font-weight: 500;
    padding: 0.5rem 1rem !important;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
    position: relative;
}

.nav-link:hover {
    color: var(--secondary-color) !important;
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateY(-1px);
}

.nav-link.active {
    background-color: rgba(255, 255, 0, 0.2) !important;
    color: var(--secondary-color) !important;
}

/* Header Section */
.header-section {
    background: var(--gradient-primary);
    color: white;
    padding: 4rem 0 3rem;
    margin-bottom: 2rem;
    position: relative;
    overflow: hidden;
}

.header-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 100" fill="rgba(255,255,0,0.05)"><polygon points="1000,100 1000,0 0,100"/></svg>');
    background-size: cover;
}

.header-content {
    position: relative;
    z-index: 2;
}

.header-section h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    background: linear-gradient(45deg, #ffffff, var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.header-section p {
    font-size: 1.1rem;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 0;
}

/* Main Content */
.main-container {
    flex: 1 0 auto;
    padding: 2rem 0;
}

.order-list {
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: var(--shadow-md);
    border: 1px solid rgba(0, 0, 0, 0.05);
    position: relative;
    overflow: hidden;
}

.order-list::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-secondary);
}

.order-list h2 {
    font-size: 1.75rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 1.5rem;
}

/* Order Items */
.order-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    border-radius: 0.5rem;
    margin-bottom: 0.5rem;
}

.order-item:hover {
    background-color: rgba(255, 255, 0, 0.05);
    transform: translateX(4px);
    box-shadow: var(--shadow-sm);
}

.order-item:last-child {
    border-bottom: none;
}

.order-info strong {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
}

.order-info .text-muted {
    color: var(--text-secondary) !important;
    font-weight: 500;
}

.order-info div:last-child {
    margin-top: 0.5rem;
    font-size: 0.9rem;
    color: var(--text-secondary);
}

/* Order Actions */
.order-actions {
    display: flex;
    gap: 0.5rem;
}

/* Buttons */
.btn-primary {
    background: var(--gradient-primary);
    border: none;
    color: var(--secondary-color);
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
    color: var(--secondary-color);
}

.btn-outline-primary {
    border-color: var(--primary-color);
    color: var(--primary-color);
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--secondary-color);
    transform: translateY(-1px);
}

.btn-outline-danger {
    border-color: #dc3545;
    color: #dc3545;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-outline-danger:hover {
    background-color: #dc3545;
    border-color: #dc3545;
    color: white;
    transform: translateY(-1px);
}

.btn-outline-info {
    border-color: #17a2b8;
    color: #17a2b8;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-outline-info:hover {
    background-color: #17a2b8;
    border-color: #17a2b8;
    color: white;
    transform: translateY(-1px);
}

/* Modals */
.modal-content {
    border-radius: 1rem;
    border: none;
    box-shadow: var(--shadow-xl);
}

.modal-header {
    background: var(--gradient-primary);
    color: white;
    border-radius: 1rem 1rem 0 0;
    border-bottom: none;
}

.modal-header .modal-title {
    font-weight: 600;
    color: var(--secondary-color);
}

.modal-header .btn-close {
    filter: brightness(0) invert(1);
}

.modal-body {
    padding: 2rem;
}

.modal-footer {
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    padding: 1.5rem 2rem;
}

/* Form Styling */
.modal-body form input,
.modal-body form select,
.modal-body form textarea {
    width: 100%;
    padding: 0.75rem 1rem;
    font-size: 1rem;
    line-height: 1.5;
    color: var(--text-primary);
    background-color: #fff;
    border: 2px solid #e2e8f0;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
}

.modal-body form input:focus,
.modal-body form select:focus,
.modal-body form textarea:focus {
    border-color: var(--primary-color);
    outline: 0;
    box-shadow: 0 0 0 0.25rem rgba(0, 0, 0, 0.1);
}

/* Dark Mode Toggle */
.dark-mode-toggle-container {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.dark-mode-toggle {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.dark-mode-toggle input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: 0.4s;
    border-radius: 24px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: 0.4s;
    border-radius: 50%;
}

.dark-mode-toggle input:checked + .slider {
    background-color: var(--secondary-color);
}

.dark-mode-toggle input:checked + .slider:before {
    transform: translateX(26px);
}

.dark-mode-toggle-label {
    color: white;
    font-weight: 500;
}

/* Alerts */
.alert-info {
    background-color: rgba(255, 255, 0, 0.1);
    border-color: var(--accent-color);
    color: var(--text-primary);
    border-radius: 0.5rem;
}

/* Footer */
footer {
    flex-shrink: 0;
    background: var(--gradient-primary);
    color: white;
    padding: 2rem 0;
    text-align: center;
}

footer p {
    margin: 0;
    color: rgba(255, 255, 255, 0.8);
}

/* Dark Mode Styles */
html.dark-mode,
html.dark-mode body {
    background-color: #121212 !important;
    color: #e0e0e0;
}

html.dark-mode .main-container {
    background-color: #121212 !important;
}

html.dark-mode .container {
    background-color: #121212 !important;
}

html.dark-mode .row {
    background-color: #121212 !important;
}

html.dark-mode .col-md-10 {
    background-color: #121212 !important;
}

html.dark-mode * {
    background-color: inherit;
}

html.dark-mode .order-list {
    background-color: #1e1e1e !important;
    border-color: #444;
    color: #e0e0e0;
}

html.dark-mode .order-list h2 {
    color: #e0e0e0;
}

html.dark-mode .order-item {
    border-bottom-color: #333;
}

html.dark-mode .order-item:hover {
    background-color: rgba(255, 255, 0, 0.1);
}

html.dark-mode .order-info strong {
    color: #e0e0e0;
}

html.dark-mode .text-muted {
    color: #a0a0a0 !important;
}

html.dark-mode .modal-content {
    background-color: #1e1e1e;
    color: #e0e0e0;
}

.dark-mode .modal-body form input,
.dark-mode .modal-body form select,
.dark-mode .modal-body form textarea {
    background-color: #2c2c2c;
    border-color: #444;
    color: #e0e0e0;
}

.dark-mode .modal-body form input:focus,
.dark-mode .modal-body form select:focus,
.dark-mode .modal-body form textarea:focus {
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 0.25rem rgba(255, 255, 0, 0.25);
}

.dark-mode .dark-mode-toggle-label {
    color: #e0e0e0;
}

.dark-mode .btn-outline-primary {
    border-color: var(--secondary-color);
    color: var(--secondary-color);
}

.dark-mode .btn-outline-primary:hover {
    background-color: var(--secondary-color);
    color: var(--primary-color);
}

.dark-mode .btn-outline-info {
    border-color: #66cdaa;
    color: #66cdaa;
}

.dark-mode .btn-outline-info:hover {
    background-color: #17a2b8;
    color: white;
}

.dark-mode .btn-outline-danger {
    border-color: #ff6666;
    color: #ff6666;
}

.dark-mode .btn-outline-danger:hover {
    background-color: #dc3545;
    color: white;
}

.dark-mode .alert-info {
    background-color: rgba(255, 255, 0, 0.15);
    border-color: var(--accent-color);
    color: #e0e0e0;
}

.dark-mode footer {
    background: var(--gradient-primary);
    color: #e0e0e0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-section h1 {
        font-size: 2rem;
    }

    .order-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .order-actions {
        width: 100%;
        justify-content: flex-end;
    }
}
