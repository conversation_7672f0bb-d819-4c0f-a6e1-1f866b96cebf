#!/bin/bash

# Wait for Ollama service to be ready
echo "Waiting for Ollama service to start..."
sleep 10

# Function to check if Ollama is ready
wait_for_ollama() {
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s http://ollama:11434/api/tags > /dev/null 2>&1; then
            echo "Ollama service is ready!"
            return 0
        fi
        echo "Attempt $attempt/$max_attempts: Ollama not ready yet, waiting..."
        sleep 5
        attempt=$((attempt + 1))
    done
    
    echo "Ollama service failed to start after $max_attempts attempts"
    return 1
}

# Wait for Ollama to be ready
if wait_for_ollama; then
    echo "Pulling Llama3 model..."
    
    # Pull the llama3 model
    curl -X POST http://ollama:11434/api/pull \
        -H "Content-Type: application/json" \
        -d '{"name": "llama3"}' \
        --max-time 1800  # 30 minutes timeout for model download
    
    if [ $? -eq 0 ]; then
        echo "Llama3 model pulled successfully!"
    else
        echo "Failed to pull Llama3 model"
        exit 1
    fi
else
    echo "Failed to connect to Ollama service"
    exit 1
fi

echo "Ollama initialization completed!"
