===== .:\Users\azizj\OneDrive\Documents\Aziz\GS\gs_project\.gitignore =====
Byte-compiled / optimized / DLL files

pycache/ *.py[cod] *$py.class

C extensions

*.so

Distribution / packaging

.Python build/ develop-eggs/ dist/ downloads/ eggs/ .eggs/ lib/ lib64/ parts/ sdist/ var/ wheels/ share/python-wheels/ *.egg-info/ .installed.cfg *.egg MANIFEST

PyInstaller

*.manifest *.spec

Installer logs

pip-log.txt pip-delete-this-directory.txt

Unit test / coverage reports

htmlcov/ .tox/ .nox/ .coverage .coverage.* .cache nosetests.xml coverage.xml *.cover *.py,cover .hypothesis/ .pytest_cache/ cover/

Translations

*.mo *.pot

Django stuff

*.log local_settings.py db.sqlite3

Flask stuff

instance/ .webassets-cache

Scrapy stuff

.scrapy

Sphinx documentation

docs/_build/ docs/_static/ docs/_templates/

PyBuilder

target/

Jupyter Notebook

.ipynb_checkpoints

IPython

profile_default/ ipython_config.py

pyenv

.python-version

celery beat schedule file

celerybeat-schedule

SageMath parsed files

*.sage.py

Environments

.env .venv env/ venv/ ENV/ env.bak/ venv.bak/

Spyder project settings

.spyderproject .spyproject

Rope project settings

.ropeproject

mkdocs documentation

/site

mypy

.mypy_cache/ .dmypy.json dmypy.json

Pyre type checker

.pyre/

pytype static type analyzer

.pytype/

Cython debug symbols

cython_debug/

Project specific

upload_excel.xlsx architecture.txt static/

Docker

Dockerfile docker-compose.yml staticfiles/ media/ postgres_data/

===== .:\Users\azizj\OneDrive\Documents\Aziz\GS\gs_project\architecture.txt =====
Folder PATH listing
Volume serial number is 4EF7-0B6A
C:.
�   architecture.txt
�   manage.py
�   upload_excel.xlsx
�   
+---gs_app
�   �   admin.py
�   �   apps.py
�   �   forms.py
�   �   models.py
�   �   tests.py
�   �   urls.py
�   �   utils.py
�   �   views.py
�   �   __init__.py
�   �   
�   +---migrations
�   �   �   0001_initial.py
�   �   �   __init__.py
�   �   �   
�   �   +---__pycache__
�   �           0001_initial.cpython-313.pyc
�   �           __init__.cpython-313.pyc
�   �           
�   +---templates
�   �   �   home.html
�   �   �   upload.html
�   �   �   
�   �   +---orders
�   �   �       add_order.html
�   �   �       orderform
�   �   �       order_confirm_delete.html
�   �   �       order_detail_confirm_delete.html
�   �   �       order_detail_form.html
�   �   �       order_detail_list.html
�   �   �       order_form.html
�   �   �       order_list.html
�   �   �       
�   �   +---products
�   �   �       product_confirm_delete.htm
�   �   �       product_form.html
�   �   �       product_list.html
�   �   �       
�   �   +---registration
�   �   �       login.html
�   �   �       
�   �   +---suppliers
�   �           add_supplier.html
�   �           delete_supplier.html
�   �           edit_supplier.html
�   �           supplier_list.html
�   �           
�   +---__pycache__
�           admin.cpython-313.pyc
�           apps.cpython-313.pyc
�           forms.cpython-313.pyc
�           models.cpython-313.pyc
�           urls.cpython-313.pyc
�           utils.cpython-313.pyc
�           views.cpython-313.pyc
�           __init__.cpython-313.pyc
�           
+---gs_project
�   �   asgi.py
�   �   settings.py
�   �   urls.py
�   �   wsgi.py
�   �   __init__.py
�   �   
�   +---__pycache__
�           settings.cpython-313.pyc
�           urls.cpython-313.pyc
�           wsgi.cpython-313.pyc
�           __init__.cpython-313.pyc
�           
+---static
        1.png
        agil.png
        background.png
        ges.png
        rapport.png
        suivi.png
        


===== .:\Users\azizj\OneDrive\Documents\Aziz\GS\gs_project\docker-compose.yml =====
version: '3.9'

services:
  web:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=**********************************/gs_mng_db
      - SECRET_KEY=django-insecure-vym%sk*1humbpjbh*_f=cb-+4@v59e+h7ahe^clcl3&tst3si+
      - DEBUG=True
      - EMAIL_HOST_USER=<EMAIL>
      - EMAIL_HOST_PASSWORD=phmcxtyakkmocbsq
      - OLLAMA_HOST=http://ollama:11434
    volumes:
      - .:/app
      - staticfiles:/app/staticfiles
      - media:/app/media
    depends_on:
      - db
      - ollama
    networks:
      - app-network

  db:
    image: postgres:15
    environment:
      - POSTGRES_DB=gs_mng_db
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=1234
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - app-network

  ollama:
    image: ollama/ollama:latest
    container_name: ollama
    ports:
      - "11434:11434"
    volumes:
      - ollama_data:/root/.ollama
    environment:
      - OLLAMA_ORIGINS=*
    networks:
      - app-network
    restart: unless-stopped
    # For GPU support (uncomment if you have NVIDIA GPU)
    # deploy:
    #   resources:
    #     reservations:
    #       devices:
    #         - driver: nvidia
    #           count: 1
    #           capabilities: [gpu]

  ollama-init:
    image: curlimages/curl:latest
    container_name: ollama-init
    depends_on:
      - ollama
    networks:
      - app-network
    volumes:
      - ./init-ollama.sh:/init-ollama.sh
    command: sh /init-ollama.sh
    restart: "no"

volumes:
  postgres_data:
  staticfiles:
  media:
  ollama_data:

networks:
  app-network:
    driver: bridge

===== .:\Users\azizj\OneDrive\Documents\Aziz\GS\gs_project\Dockerfile =====
# Use a slim Python image for smaller size
FROM python:3.11-slim

# Set working directory
WORKDIR /app

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    libpq-dev \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy project files
COPY . .

# Collect static files (assuming STATIC_ROOT is set in settings.py)
RUN python manage.py collectstatic --noinput

# Expose port
EXPOSE 8000

# Run migrations and start the server
CMD ["sh", "-c", " python manage.py makemigrations && python manage.py migrate && python manage.py runserver 0.0.0.0:8000"]

===== .:\Users\azizj\OneDrive\Documents\Aziz\GS\gs_project\init-ollama.sh =====
#!/bin/bash

# Wait for Ollama service to be ready
echo "Waiting for Ollama service to start..."
sleep 10

# Function to check if Ollama is ready
wait_for_ollama() {
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s http://ollama:11434/api/tags > /dev/null 2>&1; then
            echo "Ollama service is ready!"
            return 0
        fi
        echo "Attempt $attempt/$max_attempts: Ollama not ready yet, waiting..."
        sleep 5
        attempt=$((attempt + 1))
    done
    
    echo "Ollama service failed to start after $max_attempts attempts"
    return 1
}

# Wait for Ollama to be ready
if wait_for_ollama; then
    echo "Pulling Llama3 model..."
    
    # Pull the llama3 model
    curl -X POST http://ollama:11434/api/pull \
        -H "Content-Type: application/json" \
        -d '{"name": "llama3"}' \
        --max-time 1800  # 30 minutes timeout for model download
    
    if [ $? -eq 0 ]; then
        echo "Llama3 model pulled successfully!"
    else
        echo "Failed to pull Llama3 model"
        exit 1
    fi
else
    echo "Failed to connect to Ollama service"
    exit 1
fi

echo "Ollama initialization completed!"


===== .:\Users\azizj\OneDrive\Documents\Aziz\GS\gs_project\manage.py =====
#!/usr/bin/env python
"""Django's command-line utility for administrative tasks."""
import os
import sys


def main():
    """Run administrative tasks."""
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gs_project.settings')
    try:
        from django.core.management import execute_from_command_line
    except ImportError as exc:
        raise ImportError(
            "Couldn't import Django. Are you sure it's installed and "
            "available on your PYTHONPATH environment variable? Did you "
            "forget to activate a virtual environment?"
        ) from exc
    execute_from_command_line(sys.argv)


if __name__ == '__main__':
    main()


===== .:\Users\azizj\OneDrive\Documents\Aziz\GS\gs_project\script.txt =====
#!/bin/bash

# SNDP Agile Pipeline Setup Script
# Optimized for 20GB RAM VM with Django + Ollama containers running
#
# Memory Allocation Plan (Total: ~20GB):
# - SNDP Agile Django App: ~2GB
# - Ollama + Llama3.2:1b: ~3GB
# - SonarQube + PostgreSQL: ~4GB
# - Prometheus: ~1GB
# - Grafana: ~512MB
# - ELK Stack: ~6GB
# - Jenkins: ~2GB
# - System + Buffer: ~1.5GB
# Total: ~20GB

# Exit on any error
set -e

# Check if running as root
if [ "$(id -u)" != "0" ]; then
  echo "This script must be run as root" 1>&2
  exit 1
fi

# System requirements for SonarQube and Elasticsearch
echo "Setting up system requirements for 20GB RAM VM..."
echo "vm.max_map_count=262144" >> /etc/sysctl.conf
echo "fs.file-max=65536" >> /etc/sysctl.conf
echo "vm.swappiness=10" >> /etc/sysctl.conf
sysctl -p

# Install Docker and Docker Compose if not present
echo "Installing Docker and dependencies..."
if ! command -v docker &> /dev/null; then
  apt-get update
  apt-get install -y docker.io curl wget gnupg lsb-release
fi

# Install latest Docker Compose
if ! command -v docker-compose &> /dev/null; then
  curl -L "https://github.com/docker/compose/releases/download/v2.24.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
  chmod +x /usr/local/bin/docker-compose
fi

# Check Docker socket access
if [ ! -S /var/run/docker.sock ]; then
  echo "Docker socket not found at /var/run/docker.sock"
  exit 1
fi

# Enable and start Docker service
systemctl enable docker
systemctl start docker

# Add user to docker group
usermod -aG docker aziz1

# Configure DNS for Docker with optimized settings for 20GB RAM
mkdir -p /etc/docker
cat << EOF > /etc/docker/daemon.json
{
  "dns": ["8.8.8.8", "8.8.4.4"],
  "log-driver": "json-file",
  "log-opts": {
    "max-size": "10m",
    "max-file": "3"
  },
  "default-ulimits": {
    "nofile": {
      "Name": "nofile",
      "Hard": 65536,
      "Soft": 65536
    }
  }
}
EOF

# Reload Docker configuration
systemctl restart docker
sleep 10

# Create directories for Docker Compose files
mkdir -p /home/<USER>/sonarqube /home/<USER>/prometheus /home/<USER>/grafana /home/<USER>/elk /home/<USER>/jenkins
chown -R aziz1:aziz1 /home/<USER>
chmod -R 755 /home/<USER>

# SonarQube docker-compose.yml (Optimized for 20GB RAM)
cat << EOF > /home/<USER>/sonarqube/docker-compose.yml
version: '3.8'

services:
  postgres:
    image: postgres:15-alpine
    container_name: sonarqube_postgres
    environment:
      POSTGRES_USER: sonar
      POSTGRES_PASSWORD: sonar123
      POSTGRES_DB: sonarqube
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --lc-collate=C --lc-ctype=C"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U sonar -d sonarqube"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s
    networks:
      - sonarqube_net
    mem_limit: 1g
    mem_reservation: 512m
    cpus: "1.0"
    restart: unless-stopped

  sonarqube:
    image: sonarqube:10.6.0-community
    container_name: sonarqube_server
    ports:
      - "9000:9000"
    environment:
      SONAR_JDBC_URL: *****************************************
      SONAR_JDBC_USERNAME: sonar
      SONAR_JDBC_PASSWORD: sonar123
      SONAR_ES_BOOTSTRAP_CHECKS_DISABLE: "true"
      SONAR_WEB_JAVAOPTS: "-Xmx1g -Xms512m -XX:+UseG1GC"
      SONAR_CE_JAVAOPTS: "-Xmx1g -Xms512m -XX:+UseG1GC"
      SONAR_SEARCH_JAVAOPTS: "-Xmx512m -Xms256m -XX:+UseG1GC"
    volumes:
      - sonarqube_data:/opt/sonarqube/data
      - sonarqube_logs:/opt/sonarqube/logs
      - sonarqube_extensions:/opt/sonarqube/extensions
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - sonarqube_net
    mem_limit: 3g
    mem_reservation: 2g
    cpus: "1.0"
    restart: unless-stopped
    ulimits:
      nofile:
        soft: 65536
        hard: 65536
volumes:
  sonarqube_data:
  sonarqube_logs:
  sonarqube_extensions:
  postgres_data:
networks:
  sonarqube_net:
    driver: bridge
EOF
chown aziz1:aziz1 /home/<USER>/sonarqube/docker-compose.yml
chmod 644 /home/<USER>/sonarqube/docker-compose.yml

# Prometheus docker-compose.yml (Optimized for 20GB RAM)
cat << EOF > /home/<USER>/prometheus/docker-compose.yml
version: '3.8'

services:
  prometheus:
    image: prom/prometheus:v2.54.1
    container_name: prometheus_server
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=30d'
      - '--storage.tsdb.retention.size=10GB'
      - '--web.enable-lifecycle'
      - '--web.enable-admin-api'
    mem_limit: 1g
    mem_reservation: 512m
    cpus: "0.5"
    restart: unless-stopped

volumes:
  prometheus_data:
EOF
chown aziz1:aziz1 /home/<USER>/prometheus/docker-compose.yml
chmod 644 /home/<USER>/prometheus/docker-compose.yml

# Prometheus configuration (Updated for SNDP Agile monitoring)
cat << EOF > /home/<USER>/prometheus/prometheus.yml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  - job_name: 'sndp-agile-django'
    static_configs:
      - targets: ['host.docker.internal:8000']
    metrics_path: '/metrics'
    scrape_interval: 30s
    scrape_timeout: 10s

  - job_name: 'sonarqube'
    static_configs:
      - targets: ['host.docker.internal:9000']
    metrics_path: '/api/monitoring/metrics'
    scrape_interval: 60s
    scrape_timeout: 30s

  - job_name: 'grafana'
    static_configs:
      - targets: ['host.docker.internal:3000']
    metrics_path: '/metrics'
    scrape_interval: 60s

  - job_name: 'jenkins'
    static_configs:
      - targets: ['host.docker.internal:8080']
    metrics_path: '/prometheus'
    scrape_interval: 60s

  - job_name: 'node-exporter'
    static_configs:
      - targets: ['host.docker.internal:9100']
    scrape_interval: 30s

  - job_name: 'docker'
    static_configs:
      - targets: ['host.docker.internal:9323']
    scrape_interval: 30s
EOF
chown aziz1:aziz1 /home/<USER>/prometheus/prometheus.yml
chmod 644 /home/<USER>/prometheus/prometheus.yml

# Grafana docker-compose.yml (Optimized for 20GB RAM)
cat << EOF > /home/<USER>/grafana/docker-compose.yml
version: '3.8'

services:
  grafana:
    image: grafana/grafana:11.2.0
    container_name: grafana_server
    ports:
      - "3000:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./grafana.ini:/etc/grafana/grafana.ini:ro
      - ./dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./datasources:/etc/grafana/provisioning/datasources:ro
    environment:
      GF_SECURITY_ADMIN_USER: aziz
      GF_SECURITY_ADMIN_PASSWORD: aziz123
      GF_INSTALL_PLUGINS: grafana-piechart-panel,grafana-worldmap-panel,grafana-clock-panel
      GF_RENDERING_SERVER_URL: http://renderer:8081/render
      GF_RENDERING_CALLBACK_URL: http://grafana:3000/
      GF_LOG_FILTERS: rendering:debug
    mem_limit: 512m
    mem_reservation: 256m
    cpus: "0.3"
    restart: unless-stopped

volumes:
  grafana_data:
EOF
chown aziz1:aziz1 /home/<USER>/grafana/docker-compose.yml
chmod 644 /home/<USER>/grafana/docker-compose.yml

# ELK docker-compose.yml (Optimized for 20GB RAM)
cat << EOF > /home/<USER>/elk/docker-compose.yml
version: '3.8'

services:
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.15.1
    container_name: elasticsearch_server
    ports:
      - "9200:9200"
      - "9300:9300"
    environment:
      discovery.type: single-node
      xpack.security.enabled: "false"
      xpack.monitoring.collection.enabled: "true"
      ES_JAVA_OPTS: "-Xms4g -Xmx4g"
      bootstrap.memory_lock: "true"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    ulimits:
      memlock:
        soft: -1
        hard: -1
      nofile:
        soft: 65536
        hard: 65536
    mem_limit: 6g
    mem_reservation: 4g
    cpus: "2.0"
    restart: unless-stopped
  logstash:
    image: docker.elastic.co/logstash/logstash:8.15.1
    ports:
      - "5044:5044"
    volumes:
      - ./logstash.conf:/usr/share/logstash/pipeline/logstash.conf
    depends_on:
      - elasticsearch
    mem_limit: 256m
    mem_reservation: 128m
    cpus: "0.2"
    restart: always
  kibana:
    image: docker.elastic.co/kibana/kibana:8.15.1
    ports:
      - "5601:5601"
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    depends_on:
      - elasticsearch
    mem_limit: 256m
    mem_reservation: 128m
    cpus: "0.2"
    restart: always
  filebeat:
    image: docker.elastic.co/beats/filebeat:8.15.1
    volumes:
      - ./filebeat.yml:/usr/share/filebeat/filebeat.yml
      - /var/log:/var/log:ro
    depends_on:
      - logstash
    mem_limit: 256m
    mem_reservation: 128m
    cpus: "0.2"
    restart: always
volumes:
  elasticsearch_data:
EOF
chown aziz1:aziz1 /home/<USER>/elk/docker-compose.yml
chmod 644 /home/<USER>/elk/docker-compose.yml

# Logstash configuration
cat << EOF > /home/<USER>/elk/logstash.conf
input {
  beats {
    port => 5044
  }
}
output {
  elasticsearch {
    hosts => ["http://elasticsearch:9200"]
    index => "agil-app-%{+YYYY.MM.dd}"
  }
}
EOF
chown aziz1:aziz1 /home/<USER>/elk/logstash.conf
chmod 644 /home/<USER>/elk/logstash.conf

# Filebeat configuration
cat << EOF > /home/<USER>/elk/filebeat.yml
filebeat.inputs:
  - type: kubernetes
    enabled: true
    paths:
      - /var/log/containers/*.log
    processors:
      - add_kubernetes_metadata:
          host: \${NODE_NAME}
          matchers:
          - logs_path:
              logs_path: "/var/log/containers/"
output.logstash:
  hosts: ["logstash:5044"]
EOF
chown aziz1:aziz1 /home/<USER>/elk/filebeat.yml
chmod 644 /home/<USER>/elk/filebeat.yml

# Jenkins docker-compose.yml
cat << EOF > /home/<USER>/jenkins/docker-compose.yml
services:
  jenkins:
    image: jenkins/jenkins:lts-jdk17
    user: root
    command: >
      sh -c "apt-get update && apt-get install -y docker.io && usermod -aG docker jenkins && su jenkins -c 'java -jar /usr/share/jenkins/jenkins.war'"
    ports:
      - "8080:8080"
      - "50000:50000"
    volumes:
      - jenkins_home:/var/jenkins_home
      - ./init.groovy.d:/var/jenkins_home/init.groovy.d
      - /var/run/docker.sock:/var/run/docker.sock
    environment:
      - JAVA_OPTS=-Djenkins.install.runSetupWizard=false -Xmx512m -Xms256m -Dblueocean.enabled=true
    mem_limit: 1g
    mem_reservation: 512m
    cpus: "0.6"
    restart: always
volumes:
  jenkins_home:
EOF
chown aziz1:aziz1 /home/<USER>/jenkins/docker-compose.yml
chmod 644 /home/<USER>/jenkins/docker-compose.yml

# Jenkins init.groovy.d directory and script
mkdir -p /home/<USER>/jenkins/init.groovy.d
chown aziz1:aziz1 /home/<USER>/jenkins/init.groovy.d
chmod 755 /home/<USER>/jenkins/init.groovy.d

cat << EOF > /home/<USER>/jenkins/init.groovy.d/init.groovy
import jenkins.model.*
import hudson.security.*

def instance = Jenkins.getInstance()
def hudsonRealm = new HudsonPrivateSecurityRealm(false)
hudsonRealm.createAccount("aziz", "aziz")
instance.setSecurityRealm(hudsonRealm)
def strategy = new FullControlOnceLoggedInAuthorizationStrategy()
strategy.setAllowAnonymousRead(false)
instance.setAuthorizationStrategy(strategy)
instance.save()
EOF
chown aziz1:aziz1 /home/<USER>/jenkins/init.groovy.d/init.groovy
chmod 644 /home/<USER>/jenkins/init.groovy.d/init.groovy

# Create systemd service for Docker Compose autostart
cat << EOF > /etc/systemd/system/docker-compose-autostart.service
[Unit]
Description=Docker Compose Services
Requires=docker.service
After=docker.service

[Service]
Type=oneshot
RemainAfterExit=yes
ExecStart=/usr/bin/docker-compose -f /home/<USER>/sonarqube/docker-compose.yml up -d
ExecStart=/usr/bin/docker-compose -f /home/<USER>/prometheus/docker-compose.yml up -d
ExecStart=/usr/bin/docker-compose -f /home/<USER>/grafana/docker-compose.yml up -d
ExecStart=/usr/bin/docker-compose -f /home/<USER>/elk/docker-compose.yml up -d
ExecStart=/usr/bin/docker-compose -f /home/<USER>/jenkins/docker-compose.yml up -d
ExecStop=/usr/bin/docker-compose -f /home/<USER>/sonarqube/docker-compose.yml down
ExecStop=/usr/bin/docker-compose -f /home/<USER>/prometheus/docker-compose.yml down
ExecStop=/usr/bin/docker-compose -f /home/<USER>/grafana/docker-compose.yml down
ExecStop=/usr/bin/docker-compose -f /home/<USER>/elk/docker-compose.yml down
ExecStop=/usr/bin/docker-compose -f /home/<USER>/jenkins/docker-compose.yml down
User=aziz1
WorkingDirectory=/home/<USER>

[Install]
WantedBy=multi-user.target
EOF
chmod 644 /etc/systemd/system/docker-compose-autostart.service

# Enable and start systemd service
systemctl daemon-reload
systemctl enable docker-compose-autostart
systemctl start docker-compose-autostart

# Pull Docker images
docker pull sonarqube:10.6.0-community || true
docker pull postgres:15 || true
docker pull prom/prometheus:v2.54.1 || true
docker pull grafana/grafana:11.2.0 || true
docker pull docker.elastic.co/elasticsearch/elasticsearch:8.15.1 || true
docker pull docker.elastic.co/logstash/logstash:8.15.1 || true
docker pull docker.elastic.co/kibana/kibana:8.15.1 || true
docker pull docker.elastic.co/beats/filebeat:8.15.1 || true
docker pull jenkins/jenkins:lts-jdk17 || true

# Create startup script for pipeline services
cat << EOF > /home/<USER>/start-pipeline.sh
#!/bin/bash

echo "Starting SNDP Agile Pipeline Services..."
echo "Note: Make sure your Django app with Ollama is not running to avoid memory conflicts"
echo ""

# Check available memory
AVAILABLE_MEM=\$(free -m | awk 'NR==2{printf "%.0f", \$7}')
echo "Available memory: \${AVAILABLE_MEM}MB"

if [ "\$AVAILABLE_MEM" -lt 8192 ]; then
    echo "Warning: Less than 8GB available memory. Consider stopping other services."
    read -p "Continue anyway? (y/N): " -n 1 -r
    echo
    if [[ ! \$REPLY =~ ^[Yy]\$ ]]; then
        exit 1
    fi
fi

# Start services in order
echo "Starting SonarQube..."
cd /home/<USER>/sonarqube && docker-compose up -d

echo "Starting Prometheus..."
cd /home/<USER>/prometheus && docker-compose up -d

echo "Starting Grafana..."
cd /home/<USER>/grafana && docker-compose up -d

echo "Starting ELK Stack..."
cd /home/<USER>/elk && docker-compose up -d

echo "Starting Jenkins..."
cd /home/<USER>/jenkins && docker-compose up -d

echo ""
echo "Pipeline services started! Access URLs:"
echo "  SonarQube: http://localhost:9000 (sonar/sonar123)"
echo "  Prometheus: http://localhost:9090"
echo "  Grafana: http://localhost:3000 (aziz/aziz123)"
echo "  Kibana: http://localhost:5601"
echo "  Jenkins: http://localhost:8080 (aziz/aziz)"
echo ""
echo "To stop all services: /home/<USER>/stop-pipeline.sh"
EOF

# Create stop script
cat << EOF > /home/<USER>/stop-pipeline.sh
#!/bin/bash

echo "Stopping SNDP Agile Pipeline Services..."

for dir in jenkins elk grafana prometheus sonarqube; do
    echo "Stopping \$dir..."
    cd /home/<USER>/\$dir && docker-compose down
done

echo "All pipeline services stopped."
EOF

# Make scripts executable
chmod +x /home/<USER>/start-pipeline.sh
chmod +x /home/<USER>/stop-pipeline.sh
chown aziz1:aziz1 /home/<USER>/start-pipeline.sh
chown aziz1:aziz1 /home/<USER>/stop-pipeline.sh

echo ""
echo "=== SNDP Agile Pipeline Setup Complete ==="
echo ""
echo "Memory-optimized for 20GB RAM with Django + Ollama running"
echo ""
echo "To start pipeline services:"
echo "  sudo -u aziz1 /home/<USER>/start-pipeline.sh"
echo ""
echo "To stop pipeline services:"
echo "  sudo -u aziz1 /home/<USER>/stop-pipeline.sh"
echo ""
echo "Service URLs (after starting):"
echo "  SonarQube: http://localhost:9000 (sonar/sonar123)"
echo "  Prometheus: http://localhost:9090"
echo "  Grafana: http://localhost:3000 (aziz/aziz123)"
echo "  Kibana: http://localhost:5601"
echo "  Jenkins: http://localhost:8080 (aziz/aziz)"

===== .:\Users\azizj\OneDrive\Documents\Aziz\GS\gs_project\script_fixed.sh =====
#!/bin/bash

# Exit on any error
set -e

# Check if running as root
if [ "$(id -u)" != "0" ]; then
  echo "This script must be run as root" 1>&2
  exit 1
fi

# System requirements check
echo "Checking system requirements..."
TOTAL_MEM=$(free -m | awk 'NR==2{printf "%.0f", $2}')
if [ "$TOTAL_MEM" -lt 4096 ]; then
  echo "Warning: SonarQube requires at least 4GB RAM. Current: ${TOTAL_MEM}MB"
  echo "Consider increasing VM memory or using lighter alternatives."
fi

# Set system limits for Elasticsearch (required for SonarQube)
echo "vm.max_map_count=262144" >> /etc/sysctl.conf
echo "fs.file-max=65536" >> /etc/sysctl.conf
sysctl -p

# Install Docker and Docker Compose if not present
echo "Installing Docker and Docker Compose..."
if ! command -v docker &> /dev/null; then
  apt-get update
  apt-get install -y docker.io curl
fi

# Install Docker Compose v2
if ! command -v docker-compose &> /dev/null; then
  curl -L "https://github.com/docker/compose/releases/download/v2.24.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
  chmod +x /usr/local/bin/docker-compose
fi

# Check Docker socket access
if [ ! -S /var/run/docker.sock ]; then
  echo "Docker socket not found at /var/run/docker.sock"
  exit 1
fi

# Enable and start Docker service
systemctl enable docker
systemctl start docker

# Add user to docker group
usermod -aG docker aziz1

# Configure DNS for Docker
mkdir -p /etc/docker
cat << EOF > /etc/docker/daemon.json
{
  "dns": ["8.8.8.8", "8.8.4.4"],
  "log-driver": "json-file",
  "log-opts": {
    "max-size": "10m",
    "max-file": "3"
  }
}
EOF

# Reload Docker configuration
systemctl restart docker
sleep 10

# Create directories for Docker Compose files
echo "Creating project directories..."
mkdir -p /home/<USER>/{sonarqube,prometheus,grafana,elk,jenkins}
chown -R aziz1:aziz1 /home/<USER>
chmod -R 755 /home/<USER>

# Fixed SonarQube docker-compose.yml with proper configuration
echo "Creating SonarQube configuration..."
cat << EOF > /home/<USER>/sonarqube/docker-compose.yml
version: '3.8'

services:
  postgres:
    image: postgres:15-alpine
    container_name: sonarqube_postgres
    environment:
      POSTGRES_USER: sonar
      POSTGRES_PASSWORD: sonar123
      POSTGRES_DB: sonarqube
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U sonar -d sonarqube"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s
    networks:
      - sonarqube_net
    restart: unless-stopped
    mem_limit: 512m
    cpus: "0.5"

  sonarqube:
    image: sonarqube:10.6.0-community
    container_name: sonarqube_server
    ports:
      - "9000:9000"
    environment:
      SONAR_JDBC_URL: *****************************************
      SONAR_JDBC_USERNAME: sonar
      SONAR_JDBC_PASSWORD: sonar123
      SONAR_ES_BOOTSTRAP_CHECKS_DISABLE: "true"
      SONAR_WEB_JAVAOPTS: "-Xmx1g -Xms512m"
      SONAR_CE_JAVAOPTS: "-Xmx1g -Xms512m"
      SONAR_SEARCH_JAVAOPTS: "-Xmx512m -Xms512m"
    volumes:
      - sonarqube_data:/opt/sonarqube/data
      - sonarqube_logs:/opt/sonarqube/logs
      - sonarqube_extensions:/opt/sonarqube/extensions
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - sonarqube_net
    restart: unless-stopped
    mem_limit: 2g
    cpus: "1.0"
    ulimits:
      nofile:
        soft: 65536
        hard: 65536

volumes:
  sonarqube_data:
  sonarqube_logs:
  sonarqube_extensions:
  postgres_data:

networks:
  sonarqube_net:
    driver: bridge
EOF

# Create SonarQube startup script
cat << EOF > /home/<USER>/sonarqube/start.sh
#!/bin/bash
echo "Starting SonarQube services..."
cd /home/<USER>/sonarqube
docker-compose down || true
docker-compose pull
docker-compose up -d

echo "Waiting for SonarQube to start..."
timeout=300
counter=0
while [ \$counter -lt \$timeout ]; do
    if curl -s http://localhost:9000/api/system/status | grep -q "UP"; then
        echo "SonarQube is ready!"
        echo "Access SonarQube at: http://localhost:9000"
        echo "Default credentials: admin/admin"
        break
    fi
    echo "Waiting for SonarQube... (\$counter/\$timeout seconds)"
    sleep 10
    counter=\$((counter + 10))
done

if [ \$counter -ge \$timeout ]; then
    echo "SonarQube failed to start within \$timeout seconds"
    docker-compose logs sonarqube
    exit 1
fi
EOF

chmod +x /home/<USER>/sonarqube/start.sh
chown aziz1:aziz1 /home/<USER>/sonarqube/start.sh

# Prometheus docker-compose.yml (optimized)
echo "Creating Prometheus configuration..."
cat << EOF > /home/<USER>/prometheus/docker-compose.yml
version: '3.8'

services:
  prometheus:
    image: prom/prometheus:v2.54.1
    container_name: prometheus_server
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=15d'
      - '--web.enable-lifecycle'
    restart: unless-stopped
    mem_limit: 512m
    cpus: "0.5"

volumes:
  prometheus_data:
EOF

# Prometheus configuration (updated for your Django app)
cat << EOF > /home/<USER>/prometheus/prometheus.yml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

scrape_configs:
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  - job_name: 'sndp-agile-app'
    static_configs:
      - targets: ['host.docker.internal:8000']
    metrics_path: '/metrics'
    scrape_interval: 30s

  - job_name: 'sonarqube'
    static_configs:
      - targets: ['host.docker.internal:9000']
    metrics_path: '/api/monitoring/metrics'
    scrape_interval: 60s
EOF

# Grafana docker-compose.yml (optimized)
echo "Creating Grafana configuration..."
cat << EOF > /home/<USER>/grafana/docker-compose.yml
version: '3.8'

services:
  grafana:
    image: grafana/grafana:11.2.0
    container_name: grafana_server
    ports:
      - "3000:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./grafana.ini:/etc/grafana/grafana.ini:ro
    environment:
      GF_SECURITY_ADMIN_USER: aziz
      GF_SECURITY_ADMIN_PASSWORD: aziz123
      GF_INSTALL_PLUGINS: grafana-piechart-panel
    restart: unless-stopped
    mem_limit: 512m
    cpus: "0.5"

volumes:
  grafana_data:
EOF

# Grafana configuration
cat << EOF > /home/<USER>/grafana/grafana.ini
[server]
http_port = 3000

[security]
admin_user = aziz
admin_password = aziz123

[users]
allow_sign_up = false

[analytics]
reporting_enabled = false
check_for_updates = false
EOF

# Set proper ownership
chown -R aziz1:aziz1 /home/<USER>
chmod -R 755 /home/<USER>

echo "Configuration files created successfully!"
echo ""
echo "To start SonarQube:"
echo "  sudo -u aziz1 /home/<USER>/sonarqube/start.sh"
echo ""
echo "To start other services:"
echo "  cd /home/<USER>/prometheus && docker-compose up -d"
echo "  cd /home/<USER>/grafana && docker-compose up -d"
echo ""
echo "Service URLs:"
echo "  SonarQube: http://localhost:9000 (admin/admin)"
echo "  Prometheus: http://localhost:9090"
echo "  Grafana: http://localhost:3000 (aziz/aziz123)"


===== .:\Users\azizj\OneDrive\Documents\Aziz\GS\gs_project\gs_app\admin.py =====
from django.contrib import admin

# Register your models here.


===== .:\Users\azizj\OneDrive\Documents\Aziz\GS\gs_project\gs_app\apps.py =====
from django.apps import AppConfig


class GsAppConfig(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'gs_app'


===== .:\Users\azizj\OneDrive\Documents\Aziz\GS\gs_project\gs_app\forms.py =====
from django import forms
from django.core.validators import RegexValidator
from .models import Product, Supplier, Order, OrderDetail

# Formulaire pour Product
class ProductForm(forms.ModelForm):
    class Meta:
        model = Product
        fields = ['name', 'category', 'supplier', 'quantity_in_stock', 'price']

# Formulaire pour Supplier
class SupplierForm(forms.ModelForm):
    # Add a validator to ensure contact is exactly 8 digits
    contact = forms.CharField(
        validators=[
            RegexValidator(
                regex=r'^\d{8}$',
                message='Le numéro de contact doit contenir exactement 8 chiffres.',
                code='invalid_contact'
            )
        ],
        widget=forms.TextInput(attrs={'pattern': '[0-9]{8}', 'title': 'Le numéro doit contenir exactement 8 chiffres'})
    )
    
    class Meta:
        model = Supplier
        fields = ['name', 'contact']

class OrderForm(forms.ModelForm):
    products = forms.ModelMultipleChoiceField(
        queryset=Product.objects.all(),
        widget=forms.CheckboxSelectMultiple,
        required=True
    )
    quantities = forms.CharField(
        widget=forms.HiddenInput(),
        required=False
    )

    class Meta:
        model = Order
        fields = ['status', 'products', 'quantities']

class OrderDetailForm(forms.ModelForm):
    class Meta:
        model = OrderDetail
        fields = ['product', 'quantity', 'price_at_order']


===== .:\Users\azizj\OneDrive\Documents\Aziz\GS\gs_project\gs_app\models.py =====
from django.db import models


###  Modèle Catégorie
class Category(models.Model):
    name = models.CharField(max_length=100, unique=True)

    def __str__(self):
        return self.name

    # Relation One-to-Many avec Product
    def get_products(self):
        return self.products.all()

###  Modèle Fournisseur
class Supplier(models.Model):
    name = models.CharField(max_length=100)
    contact = models.CharField(max_length=100, blank=True, null=True)

    def __str__(self):
        return self.name

    # Relation One-to-Many avec Product
    def get_products(self):
        return self.products.all()

### Modèle Produit
class Product(models.Model):
    name = models.CharField(max_length=100)
    
    # Relation avec Category (One-to-Many)
    category = models.ForeignKey(Category, on_delete=models.CASCADE, related_name='products')
    
    # Relation avec Supplier (One-to-Many)
    supplier = models.ForeignKey(Supplier, on_delete=models.SET_NULL, null=True, blank=True, related_name='products')
    
    quantity_in_stock = models.PositiveIntegerField(default=0)
    price = models.DecimalField(max_digits=10, decimal_places=2)

    def __str__(self):
        return f"{self.name} ({self.category.name})"

    # Relation avec OrderDetail (Many-to-Many via OrderDetail)
    def get_orders(self):
        return self.order_details.all()

###  Modèle Commande
class Order(models.Model):
    date = models.DateTimeField(auto_now_add=True)
    status = models.CharField(max_length=50, choices=[('pending', 'En attente'), ('completed', 'Terminée')])

    def __str__(self):
        return f"Commande {self.id} - {self.status}"

    # Relation One-to-Many avec OrderDetail
    def get_order_details(self):
        return self.order_details.all()

    # Liste des produits commandés
    def get_products(self):
        return [detail.product for detail in self.order_details.all()]
    
    # Calcul du prix total de la commande
    def get_total_price(self):
        total = 0
        for detail in self.order_details.all():
            total += detail.get_total()
        return total

###  Modèle Détail de Commande (Relation Many-to-Many entre Order et Product)
class OrderDetail(models.Model):
    # Relation avec Order (One-to-Many)
    order = models.ForeignKey(Order, on_delete=models.CASCADE, related_name='order_details')
    
    # Relation avec Product (One-to-Many)
    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='order_details')

    quantity = models.PositiveIntegerField()
    price_at_order = models.DecimalField(max_digits=10, decimal_places=2)

    def __str__(self):
        return f"{self.quantity} x {self.product.name} (Commande {self.order.id})"
    
    def get_total(self):
        return self.quantity * self.price_at_order


===== .:\Users\azizj\OneDrive\Documents\Aziz\GS\gs_project\gs_app\tests.py =====
from django.test import TestCase

# Create your tests here.


===== .:\Users\azizj\OneDrive\Documents\Aziz\GS\gs_project\gs_app\urls.py =====
from gs_app.views import upload_excel
from django.urls import path
from . import views






urlpatterns = [
    path('', views.home, name='home'),  # Page d'accueil
    path('products/', views.product_list, name='product_list'),  # Liste des produits
    path('products/add/', views.add_product, name='add_product'),  # Ajout d'un produit
    path('products/edit/<int:product_id>/', views.edit_product, name='edit_product'),  # Modification
    path('products/delete/<int:product_id>/', views.delete_product, name='delete_product'),  # Suppression
    path('suppliers/', views.supplier_list, name='supplier_list'),  # Liste des fournisseurs
    path('suppliers/add/', views.add_supplier, name='add_supplier'),  # Ajouter un fournisseur
    path('suppliers/<int:id>/', views.supplier_detail, name='supplier_detail'),  # Détails d'un fournisseur
    path('suppliers/<int:id>/edit/', views.edit_supplier, name='edit_supplier'),  # Modifier un fournisseur
    path('suppliers/<int:id>/delete/', views.delete_supplier, name='delete_supplier'),  # Supprimer un fournisseur
    path('orders/', views.order_list, name='order_list'),  # Liste des commandes
    path('orders/add/', views.add_order, name='add_order'),  # Ajouter une commande
    path('orders/edit/<int:order_id>/', views.edit_order, name='edit_order'),  # Modifier une commande
    path('orders/delete/<int:order_id>/', views.delete_order, name='delete_order'),  # Supprimer une commande
    path('orders/<int:order_id>/details/', views.order_detail_list, name='order_detail_list'),  # Détails d'une commande
    path('orders/<int:order_id>/details/add/', views.add_order_detail, name='add_order_detail'),  # Ajouter un détail
    path('orders/details/edit/<int:order_detail_id>/', views.edit_order_detail, name='edit_order_detail'),  # Modifier un détail
    path('orders/details/delete/<int:order_detail_id>/', views.delete_order_detail, name='delete_order_detail'),  # Supprimer un détail
    path('ollama-chat/', views.ollama_chat, name='ollama_chat'),
    path('upload-stock/', upload_excel, name='upload_excel'),
    path('dashboard/', views.dashboard, name='dashboard'),
    path('map/', views.map_view, name='map'),
    path('translate/', views.translate_text, name='translate'),
    
    
]


===== .:\Users\azizj\OneDrive\Documents\Aziz\GS\gs_project\gs_app\utils.py =====
import pandas as pd
import requests

def prepare_prompt_from_df(df):
    if 'quantity' in df.columns and 'min_required' in df.columns:
        low_stock = df[df['quantity'] < df['min_required']]
        summary = low_stock[['product_name', 'quantity', 'min_required']].to_dict(orient='records')
    else:
        summary = df.head(5).to_dict(orient='records')

    prompt = f"""
You are an inventory advisor. Based on the data below, advise which items need restocking and why:

{summary}

Give suggestions on what to reorder, how much, and any optimization tips.
"""
    return prompt

def translate_fr_to_en(text):
    """
    Translate French text to English using an external API
    
    Args:
        text (str): The French text to translate
        
    Returns:
        str: The translated English text, or the original text if translation fails
    """
    try:
        # Using a free translation API
        url = "https://translate.googleapis.com/translate_a/single"
        params = {
            "client": "gtx",
            "sl": "fr",  # Source language: French
            "tl": "en",  # Target language: English
            "dt": "t",   # Return translated text
            "q": text    # Text to translate
        }
        
        response = requests.get(url, params=params)
        if response.status_code == 200:
            # Parse the response
            result = response.json()
            translated_text = ''.join([sentence[0] for sentence in result[0]])
            return translated_text
        else:
            return text  # Return original text if API call fails
    except Exception as e:
        # Log the error but return the original text to avoid breaking the app
        print(f"Translation error: {str(e)}")
        return text


===== .:\Users\azizj\OneDrive\Documents\Aziz\GS\gs_project\gs_app\views.py =====
import json
import logging
import requests
import pandas as pd
import os
from datetime import datetime, timedelta
from django.utils.timezone import now
from django.db.models import Sum
from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.contrib import messages
from django.core.mail import send_mail
from django.conf import settings

from .models import Product, Supplier, Order, OrderDetail
from .forms import ProductForm, SupplierForm, OrderForm, OrderDetailForm
from .utils import translate_fr_to_en, prepare_prompt_from_df

logger = logging.getLogger(__name__)

# Get Ollama host from environment variable or default to localhost
OLLAMA_HOST = os.getenv('OLLAMA_HOST', 'http://localhost:11434')


@login_required
def home(request):
    return render(request, 'home.html')

# Ajouter un produit
def add_product(request):
    if request.method == 'POST':
        form = ProductForm(request.POST)
        if form.is_valid():
            product = form.save()

            # Check if restock was requested
            restock = request.POST.get('restock', 'no') == 'yes'

            if restock:
                # Send email notification about restocking
                subject = f'SNDP Agile: Nouveau produit ajouté - {product.name}'
                message = f"""
Bonjour,

Nous vous informons qu'un nouveau produit a été ajouté à l'inventaire et réapprovisionné:

DÉTAILS DU PRODUIT:
------------------------------------------
Nom du produit: {product.name}
Catégorie: {product.category}
Fournisseur: {product.supplier}
Quantité en stock: {product.quantity_in_stock} unités
Prix unitaire: {product.price}€
------------------------------------------

Cette opération a été effectuée le {datetime.now().strftime('%d/%m/%Y à %H:%M')}.

Pour plus de détails, veuillez vous connecter au système de gestion de stock SNDP Agile.

Cordialement,
L'équipe SNDP Agile
<EMAIL>
+216 123 456 789

------------------------------------------
Ce message est généré automatiquement. Merci de ne pas y répondre.
                """

                try:
                    send_mail(
                        subject=subject,
                        message=message,
                        from_email=settings.DEFAULT_FROM_EMAIL,
                        recipient_list=['<EMAIL>'],
                        fail_silently=False,
                    )
                    messages.success(request, f'Produit ajouté et notification envoyée.')
                except Exception as e:
                    messages.error(request, f'Produit ajouté mais erreur d\'envoi d\'email: {str(e)}')
            else:
                messages.success(request, 'Produit ajouté avec succès.')

            return redirect('product_list')
    else:
        form = ProductForm()

    # If format=form is in the query params, return only the form HTML
    if request.GET.get('format') == 'form':
        return render(request, 'products/product_form_fields.html', {'form': form})

    return render(request, 'products/product_form.html', {'form': form})

# Modifier un produit

# Supprimer un produit
def delete_product(request, product_id):
    product = get_object_or_404(Product, id=product_id)
    if request.method == "POST":
        product.delete()
        return redirect('product_list')
    return render(request, 'products/product_confirm_delete.html', {'product': product})

# Liste des produits
def product_list(request):
    products = Product.objects.all()
    return render(request, 'products/product_list.html', {'products': products})

# Ajouter un fournisseur
def add_supplier(request):
    if request.method == 'POST':
        form = SupplierForm(request.POST)
        if form.is_valid():
            form.save()
            if request.headers.get('x-requested-with') == 'XMLHttpRequest':
                return JsonResponse({"message": "Fournisseur ajouté avec succès!"}, status=201)
            return redirect('supplier_list')
    else:
        form = SupplierForm()

    if request.GET.get('format') == 'form':
        # Return only the form for AJAX requests
        return render(request, 'suppliers/supplier_form_fields.html', {'form': form})

    return render(request, 'suppliers/add_supplier.html', {'form': form})

# Liste des fournisseurs
def supplier_list(request):
    suppliers = Supplier.objects.all()  # Récupère tous les fournisseurs
    return render(request, 'suppliers/supplier_list.html', {'suppliers': suppliers})

# Détails d'un fournisseur
def supplier_detail(request, id):
    supplier = get_object_or_404(Supplier, id=id)
    data = {
        "id": supplier.id,
        "name": supplier.name,
        "contact": supplier.contact
    }
    return JsonResponse(data)

def edit_supplier(request, id):
    supplier = get_object_or_404(Supplier, id=id)  # Get the supplier object

    if request.method == 'POST':
        form = SupplierForm(request.POST, instance=supplier)  # Bind the form to the existing supplier instance
        if form.is_valid():
            form.save()  # Save the updated supplier data
            return redirect('supplier_list')  # Redirect to the supplier list after successful update
    else:
        form = SupplierForm(instance=supplier)  # Pre-populate the form with the existing supplier data

    return render(request, 'suppliers/edit_supplier.html', {'form': form})


# Supprimer un fournisseur
def delete_supplier(request, id):
    supplier = get_object_or_404(Supplier, id=id)

    # If it's a POST request, delete the supplier
    if request.method == 'POST':
        supplier.delete()
        return redirect('supplier_list')  # Redirect to the supplier list after deletion

    # If it's a GET request, show the confirmation page
    return render(request, 'suppliers/delete_supplier.html', {'supplier': supplier})



# Liste des commandes
def order_list(request):
    orders = Order.objects.all().order_by('-date')
    return render(request, 'orders/order_list.html', {'orders': orders})

# Ajouter une commande
def add_order(request):
    products = Product.objects.all()

    if request.method == "POST":
        form = OrderForm(request.POST)
        if form.is_valid():
            order = form.save(commit=False)

            # Get the status
            status = form.cleaned_data['status']

            # Save the order
            order.save()

            # Process selected products and quantities
            selected_products = form.cleaned_data['products']
            quantities_data = request.POST.get('quantities', '')

            if quantities_data:
                quantities = {}
                for item in quantities_data.split(','):
                    if ':' in item:
                        product_id, quantity = item.split(':')
                        quantities[product_id] = int(quantity)

                # Create OrderDetail for each selected product
                for product in selected_products:
                    quantity = quantities.get(str(product.id), 1)
                    OrderDetail.objects.create(
                        order=order,
                        product=product,
                        quantity=quantity,
                        price_at_order=product.price
                    )

                    # If order is completed, update product quantities immediately
                    if status == 'completed':
                        product.quantity_in_stock += quantity
                        product.save()

            if status == 'completed':
                messages.success(request, 'Commande ajoutée comme terminée et stocks mis à jour.')
            else:
                messages.success(request, 'Commande ajoutée avec succès.')

            return redirect('order_list')
        else:
            messages.error(request, 'Erreur lors de l\'ajout de la commande.')
    else:
        form = OrderForm()

    # If format=form is in the query params, return only the form HTML
    if request.GET.get('format') == 'form':
        return render(request, 'orders/order_form_fields.html', {
            'form': form,
            'products': products
        })

    return render(request, 'orders/add_order.html', {
        'form': form,
        'products': products
    })

# Modifier une commande
def edit_order(request, order_id):
    order = get_object_or_404(Order, id=order_id)
    order_details = OrderDetail.objects.filter(order=order)
    previous_status = order.status

    if request.method == "POST":
        form = OrderForm(request.POST, instance=order)
        if form.is_valid():
            # Get the new status before saving
            new_status = form.cleaned_data['status']

            logger.info(f"Editing order #{order.id}: Status change from '{previous_status}' to '{new_status}'")

            # Check if status is changing from 'pending' to 'completed'
            if previous_status == 'pending' and new_status == 'completed':
                # Update product quantities before saving the form
                for detail in order_details:
                    product = detail.product
                    old_quantity = product.quantity_in_stock
                    product.quantity_in_stock += detail.quantity
                    logger.info(f"Updating product #{product.id} ({product.name}) quantity: {old_quantity} + {detail.quantity} = {product.quantity_in_stock}")
                    product.save()

                # Now save the form with the new status
                updated_order = form.save()
                messages.success(request, f'Commande #{order.id} terminée et stocks mis à jour.')
            else:
                # Just save the form without updating quantities
                updated_order = form.save()
                messages.success(request, f'Commande #{order.id} mise à jour avec succès.')

            # Otherwise, redirect to the order list
            return redirect('order_list')
        else:
            logger.error(f"Form errors: {form.errors}")
            messages.error(request, 'Erreur lors de la mise à jour de la commande.')

            # If it's an AJAX request, return a JSON response with errors
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return JsonResponse({'success': False, 'errors': form.errors})
    else:
        form = OrderForm(instance=order)

    # If format=form is in the query params, return only the form HTML
    if request.GET.get('format') == 'form':
        return render(request, 'orders/order_edit_form_fields.html', {
            'form': form,
            'order': order,
            'order_details': order_details
        })

    # For non-AJAX requests, redirect to order detail list
    return redirect('order_detail_list', order_id=order.id)

# Supprimer une commande
def delete_order(request, order_id):
    order = get_object_or_404(Order, id=order_id)
    if request.method == "POST":
        order.delete()
        return redirect('order_list')
    return render(request, 'orders/order_confirm_delete.html', {'order': order})

# Liste des détails d'une commande (une seule fonction)
def order_detail_list(request, order_id):
    order = get_object_or_404(Order, id=order_id)
    details = order.order_details.all()  # Assure-toi que l'instance `order_details` est définie correctement dans ton modèle `Order`
    return render(request, 'orders/order_detail_list.html', {'order': order, 'details': details})

# Ajouter un détail de commande
def add_order_detail(request, order_id):
    order = get_object_or_404(Order, id=order_id)
    if request.method == "POST":
        form = OrderDetailForm(request.POST)
        if form.is_valid():
            order_detail = form.save(commit=False)
            order_detail.order = order
            order_detail.save()
            return redirect('order_detail_list', order_id=order.id)  # Redirection vers les détails de la commande
        else:
            return render(request, 'orders/order_detail_form.html', {'form': form, 'order': order, 'error': 'Le formulaire est invalide.'})
    else:
        form = OrderDetailForm()
    return render(request, 'orders/order_detail_form.html', {'form': form, 'order': order})

# Modifier un détail de commande
def edit_order_detail(request, order_detail_id):
    order_detail = get_object_or_404(OrderDetail, id=order_detail_id)
    if request.method == "POST":
        form = OrderDetailForm(request.POST, instance=order_detail)
        if form.is_valid():
            form.save()
            return redirect('order_detail_list', order_id=order_detail.order.id)  # Redirection vers les détails de la commande
        else:
            return render(request, 'orders/order_detail_form.html', {'form': form, 'error': 'Le formulaire est invalide.'})
    else:
        form = OrderDetailForm(instance=order_detail)
    return render(request, 'orders/order_detail_form.html', {'form': form})

# Supprimer un détail de commande
def delete_order_detail(request, order_detail_id):
    order_detail = get_object_or_404(OrderDetail, id=order_detail_id)
    if request.method == "POST":
        order_detail.delete()
        return redirect('order_detail_list', order_id=order_detail.order.id)  # Redirection vers les détails de la commande
    return render(request, 'orders/order_detail_confirm_delete.html', {'order_detail': order_detail})

from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt

@csrf_exempt  # Disable CSRF for simplicity (enable in production)
def ollama_chat(request):
    if request.method == "POST":
        try:
            # Handle both form data and JSON data
            if request.content_type == 'application/json':
                import json
                data = json.loads(request.body)
                user_message = data.get("message")
            else:
                user_message = request.POST.get("message")

            if not user_message:
                return JsonResponse({"error": "No message provided"}, status=400)

            # First, check if Ollama is running
            try:
                health_response = requests.get(f"{OLLAMA_HOST}/api/tags", timeout=10)
                health_response.raise_for_status()
            except requests.RequestException:
                return JsonResponse({
                    "error": "Ollama server is not running. Please start Ollama first."
                }, status=503)

            # Make the chat request
            response = requests.post(
                f"{OLLAMA_HOST}/api/chat",
                json={
                    "model": "llama3",  # Adjust model name if needed
                    "messages": [{"role": "user", "content": user_message}],
                    "stream": False
                },
                timeout=60
            )
            response.raise_for_status()
            data = response.json()
            bot_reply = data.get("message", {}).get("content", "No reply received.")
            return JsonResponse({"message": {"content": bot_reply}})

        except requests.Timeout:
            return JsonResponse({"error": "Request timed out. Ollama might be busy."}, status=504)
        except requests.RequestException as e:
            logger.error(f"Ollama request failed: {str(e)}")
            return JsonResponse({"error": f"Request failed: {str(e)}"}, status=500)
        except ValueError as e:
            logger.error(f"JSON parsing error: {str(e)}")
            return JsonResponse({"error": "Invalid JSON response from Ollama"}, status=500)
        except Exception as e:
            logger.error(f"Unexpected error in ollama_chat: {str(e)}")
            return JsonResponse({"error": f"Unexpected error: {str(e)}"}, status=500)

    return JsonResponse({"error": "Invalid request method"}, status=405)


def ask_ollama(prompt):
    """
    Alternative method to call Ollama using HTTP API instead of subprocess
    This is more reliable and doesn't depend on hardcoded paths
    """
    try:
        # First, check if Ollama is running
        try:
            health_response = requests.get(f"{OLLAMA_HOST}/api/tags", timeout=10)
            health_response.raise_for_status()
        except requests.RequestException:
            return "Error: Ollama server is not running. Please start Ollama first."

        # Make the chat request
        response = requests.post(
            f"{OLLAMA_HOST}/api/chat",
            json={
                "model": "llama3",
                "messages": [{"role": "user", "content": prompt}],
                "stream": False
            },
            timeout=120  # Allow more time for file analysis
        )
        response.raise_for_status()
        data = response.json()
        return data.get("message", {}).get("content", "No reply received.")

    except requests.Timeout:
        return "Error: Request timed out. Ollama might be busy processing."
    except requests.RequestException as e:
        logger.error(f"Ollama request failed in ask_ollama: {str(e)}")
        return f"Error: Request failed - {str(e)}"
    except ValueError as e:
        logger.error(f"JSON parsing error in ask_ollama: {str(e)}")
        return "Error: Invalid response from Ollama"
    except Exception as e:
        logger.error(f"Unexpected error in ask_ollama: {str(e)}")
        return f"Error: Unexpected error - {str(e)}"


def upload_excel(request):
    advice = ""
    if request.method == 'POST' and request.FILES.get('excel_file'):
        excel_file = request.FILES['excel_file']
        df = pd.read_excel(excel_file)
        prompt = prepare_prompt_from_df(df)
        advice = ask_ollama(prompt)

    return render(request, 'upload.html', {'advice': advice})




def edit_product(request, product_id):
    product = get_object_or_404(Product, id=product_id)

    if request.method == 'POST':
        form = ProductForm(request.POST, instance=product)
        if form.is_valid():
            # Get restock value from form
            restock = request.POST.get('restock', 'no') == 'yes'

            # Save the form
            product = form.save()

            # Handle restock logic if needed
            if restock:
                # Send email notification about restocking
                subject = f'SNDP Agile: Réapprovisionnement de produit - {product.name}'
                message = f"""
Bonjour,

Nous vous informons qu'un produit a été réapprovisionné:

DÉTAILS DU PRODUIT:
------------------------------------------
Nom du produit: {product.name}
Catégorie: {product.category}
Fournisseur: {product.supplier}
Quantité en stock: {product.quantity_in_stock} unités
Prix unitaire: {product.price}€
------------------------------------------

Cette opération a été effectuée le {datetime.now().strftime('%d/%m/%Y à %H:%M')}.

Pour plus de détails, veuillez vous connecter au système de gestion de stock SNDP Agile.

Cordialement,
L'équipe SNDP Agile
<EMAIL>
+216 123 456 789

------------------------------------------
Ce message est généré automatiquement. Merci de ne pas y répondre.
                """

                try:
                    send_mail(
                        subject=subject,
                        message=message,
                        from_email=settings.DEFAULT_FROM_EMAIL,
                        recipient_list=['<EMAIL>'],
                        fail_silently=False,
                    )
                    messages.success(request, f'Produit mis à jour et notification envoyée.')
                except Exception as e:
                    messages.error(request, f'Produit mis à jour mais erreur d\'envoi d\'email: {str(e)}')
            else:
                messages.success(request, 'Produit mis à jour avec succès.')

            return redirect('product_list')
    else:
        form = ProductForm(instance=product)

    # Check if this is an AJAX request for the form only
    if request.GET.get('format') == 'form':
        return render(request, 'products/product_form_fields.html', {
            'form': form,
            'product': product,
            'needs_restock': product.quantity_in_stock < 100
        })

    # Regular request - return full page
    return render(request, 'products/product_form.html', {
        'form': form,
        'product': product
    })


def dashboard(request):
    total_products = Product.objects.count()
    products_in_stock = Product.objects.filter(quantity_in_stock__gt=0).count()
    out_of_stock = Product.objects.filter(quantity_in_stock=0).count()
    total_suppliers = Supplier.objects.count()
    total_orders = Order.objects.count()
    total_revenue = OrderDetail.objects.aggregate(revenue=Sum('price_at_order'))['revenue'] or 0

    # Orders per month (last 6 months)
    today = now()
    six_months_ago = today - timedelta(days=180)
    orders = Order.objects.filter(date__gte=six_months_ago)

    orders_by_month = {}
    for i in range(6):
        month = (today - timedelta(days=i*30)).replace(day=1)
        key = month.strftime("%B %Y")
        orders_by_month[key] = 0

    for order in orders:
        key = order.date.strftime("%B %Y")
        if key in orders_by_month:
            orders_by_month[key] += 1
    orders_labels = list(reversed(list(orders_by_month.keys())))
    orders_values = list(reversed(list(orders_by_month.values())))

    # Top 5 products sold
    top_products_query = OrderDetail.objects.values('product__name').annotate(
        total_sold=Sum('quantity')).order_by('-total_sold')[:5]
    top_products_labels = [item['product__name'] for item in top_products_query]
    top_products_values = [item['total_sold'] for item in top_products_query]

    context = {
        'total_products': total_products,
        'products_in_stock': products_in_stock,
        'out_of_stock': out_of_stock,
        'total_suppliers': total_suppliers,
        'total_orders': total_orders,
        'total_revenue': total_revenue,

        'orders_labels': json.dumps(orders_labels),
        'orders_values': json.dumps(orders_values),
        'top_products_labels': json.dumps(top_products_labels),
        'top_products_values': json.dumps(top_products_values),

        'low_stock_products': Product.objects.filter(quantity_in_stock__lt=5),
        'recent_orders': Order.objects.order_by('-date')[:5],
    }

    return render(request, 'dashboard.html', context)

def map_view(request):
    return render(request, 'map.html')




def translate_text(request):
    if request.method == "POST":
        text = request.POST.get("text", "")
        translated = translate_fr_to_en(text)
        return JsonResponse({"translated_text": translated})






===== .:\Users\azizj\OneDrive\Documents\Aziz\GS\gs_project\gs_app\__init__.py =====


===== .:\Users\azizj\OneDrive\Documents\Aziz\GS\gs_project\gs_app\migrations\0001_initial.py =====
# Generated by Django 5.1.6 on 2025-02-12 11:12

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Category',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
            ],
        ),
        migrations.CreateModel(
            name='Order',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateTimeField(auto_now_add=True)),
                ('status', models.CharField(choices=[('pending', 'En attente'), ('completed', 'Terminée')], max_length=50)),
            ],
        ),
        migrations.CreateModel(
            name='Supplier',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('contact', models.CharField(blank=True, max_length=100, null=True)),
            ],
        ),
        migrations.CreateModel(
            name='Product',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('quantity_in_stock', models.PositiveIntegerField(default=0)),
                ('price', models.DecimalField(decimal_places=2, max_digits=10)),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='products', to='gs_app.category')),
                ('supplier', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='products', to='gs_app.supplier')),
            ],
        ),
        migrations.CreateModel(
            name='OrderDetail',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity', models.PositiveIntegerField()),
                ('price_at_order', models.DecimalField(decimal_places=2, max_digits=10)),
                ('order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='order_details', to='gs_app.order')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='order_details', to='gs_app.product')),
            ],
        ),
    ]


===== .:\Users\azizj\OneDrive\Documents\Aziz\GS\gs_project\gs_app\migrations\__init__.py =====


===== .:\Users\azizj\OneDrive\Documents\Aziz\GS\gs_project\gs_app\migrations\__pycache__\0001_initial.cpython-313.pyc =====
�

    7��g�
  �                   �F   � S SK rS SKJrJr   " S S\R
                  5      rg)�    N)�
migrations�modelsc                   ��  � \ rS rSrSr/ r\R                  " SS\R                  " SSSSS94S\R                  " S	SS
94/S9\R                  " SS\R                  " SSSSS94S
\R                  " SS94S\R                  " SS/SS94/S9\R                  " SS\R                  " SSSSS94S\R                  " S	S94S\R                  " SS	SS94/S9\R                  " SS\R                  " SSSSS94S\R                  " S	S94S\R                  " SS94S\R                  " SSS94S \R                  " \R                   R                  R"                  R$                  S!S"S#94S$\R                  " SS\R                   R                  R"                  R&                  S!S%S&94/S9\R                  " S'S\R                  " SSSSS94S(\R                  " 5       4S)\R                  " SSS94S*\R                  " \R                   R                  R"                  R$                  S+S,S#94S-\R                  " \R                   R                  R"                  R$                  S+S.S#94/S9/rS/rg0)1�	Migration�   T�Category�idF�ID)�auto_created�primary_key�	serialize�verbose_name�name�d   )�
max_length�unique)r   �fields�Order�date)�auto_now_add�status)�pendingz
En attente)�	completedu	   Terminée�2   )�choicesr   �Supplier)r   �contact)�blankr   �null�Product�quantity_in_stockr   )�default�price�   �
   )�decimal_places�
max_digits�category�productszgs_app.category)�	on_delete�related_name�to�supplierzgs_app.supplier)r   r   r*   r+   r,   �OrderDetail�quantity�price_at_order�order�
order_detailszgs_app.order�productzgs_app.product� N)�__name__�
__module__�__qualname__�__firstlineno__�initial�dependenciesr   �CreateModelr   �BigAutoField�	CharField�
DateTimeField�PositiveIntegerField�DecimalField�
ForeignKey�django�db�deletion�CASCADE�SET_NULL�
operations�__static_attributes__r4   �    �VC:\Users\<USER>\OneDrive\Documents\Aziz\GS\gs_project\gs_app\migrations\0001_initial.pyr   r      s�  � ��G��L� 	�����v�*�*��$�Z_�nr�s�t���)�)�S��F�G��	
� 	�����v�*�*��$�Z_�nr�s�t���-�-�4�@�A��6�+�+�5N�Pj�4k�xz�{�|��	
� 	�����v�*�*��$�Z_�nr�s�t���)�)�S�9�:��F�,�,�4�C�d�S�T��	
� 	�����v�*�*��$�Z_�nr�s�t���)�)�S�9�:�$�f�&A�&A�!�&L�M��&�-�-�Q�2�N�O��V�.�.����9I�9I�9R�9R�9Z�9Z�is�  yJ�  K�  L��V�.�.�T��PV�PY�PY�P`�P`�Pi�Pi�Pr�Pr�  BL�  Qb�  c�  d�
�
	
� 	�����v�*�*��$�Z_�nr�s�t��V�8�8�:�;�!�6�#6�#6�a�TV�#W�X��&�+�+�f�i�i�6F�6F�6O�6O�6W�6W�fu�  {I�  J�  K��F�-�-��	�	�8H�8H�8Q�8Q�8Y�8Y�hw�  }M�  N�  O��		
�G-�JrI   r   )�django.db.models.deletionrB   �	django.dbr   r   r   r4   rI   rJ   �<module>rM      s   �� !� (�4�
�$�$� 4rI   

===== .:\Users\azizj\OneDrive\Documents\Aziz\GS\gs_project\gs_app\migrations\__pycache__\__init__.cpython-313.pyc =====
�

    |x�g    �                   �   � g )N� r   �    �RC:\Users\<USER>\OneDrive\Documents\Aziz\GS\gs_project\gs_app\migrations\__init__.py�<module>r      s   �r   

===== .:\Users\azizj\OneDrive\Documents\Aziz\GS\gs_project\gs_app\static\css\dashboard.css =====
/* Dashboard Professional Design with Black & Yellow Theme */
:root {
    --primary-color: #000000;
    --secondary-color: #ffff00;
    --accent-color: #ffd700;
    --dark-color: #1f1f1f;
    --light-color: #f8fafc;
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --gradient-primary: linear-gradient(135deg, #000000 0%, #333333 100%);
    --gradient-secondary: linear-gradient(135deg, #ffff00 0%, #ffd700 100%);
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

/* Base Layout */
html, body {
    height: 100%;
    margin: 0;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

body {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    background-color: var(--light-color);
    color: var(--text-primary);
    transition: all 0.3s ease;
}

.main-container {
    flex: 1;
    padding: 2rem 0;
}

/* Navigation */
.navbar {
    background: var(--gradient-primary) !important;
    backdrop-filter: blur(10px);
    box-shadow: var(--shadow-md);
    padding: 1rem 0;
    transition: all 0.3s ease;
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
    color: var(--secondary-color) !important;
    text-decoration: none;
}

.nav-link {
    color: rgba(255, 255, 255, 0.9) !important;
    font-weight: 500;
    padding: 0.5rem 1rem !important;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
    position: relative;
}

.nav-link:hover {
    color: var(--secondary-color) !important;
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateY(-1px);
}

/* Header Section */
.header-section {
    background: var(--gradient-primary);
    color: white;
    padding: 4rem 0 3rem;
    margin-bottom: 2rem;
    position: relative;
    overflow: hidden;
}

.header-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 100" fill="rgba(255,255,0,0.05)"><polygon points="1000,100 1000,0 0,100"/></svg>');
    background-size: cover;
}

.header-content {
    position: relative;
    z-index: 2;
}

.header-section h1 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    background: linear-gradient(45deg, #ffffff, var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.header-section p {
    font-size: 1rem;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 0;
}

/* Quick Actions */
.quick-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
    justify-content: center;
    margin-bottom: 2rem;
}

.quick-action-btn {
    background: white;
    border: 2px solid transparent;
    border-radius: 1rem;
    padding: 1.5rem 2rem;
    text-decoration: none;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-md);
    min-width: 200px;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.quick-action-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-secondary);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.quick-action-btn:hover::before {
    transform: scaleX(1);
}

.quick-action-btn:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
    border-color: var(--accent-color);
}

.quick-action-btn i {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    display: block;
}

.quick-action-btn span {
    font-weight: 600;
    font-size: 1rem;
}

/* Statistics Cards */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 3rem;
}

.stat-card {
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: var(--shadow-md);
    border: 1px solid rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-secondary);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
}

.stat-card:hover::before {
    transform: scaleX(1);
}

.stat-card .stat-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    display: block;
}

.stat-card .stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.stat-card .stat-label {
    color: var(--text-secondary);
    font-weight: 500;
    margin-bottom: 0;
}

/* Charts Section */
.charts-section {
    margin-bottom: 3rem;
}

.chart-container {
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: var(--shadow-md);
    border: 1px solid rgba(0, 0, 0, 0.05);
    margin-bottom: 2rem;
    position: relative;
    overflow: hidden;
}

.chart-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-secondary);
}

.chart-container h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 1.5rem;
    text-align: center;
}

/* Tables */
.table-container {
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: var(--shadow-md);
    border: 1px solid rgba(0, 0, 0, 0.05);
    margin-bottom: 2rem;
    position: relative;
    overflow: hidden;
}

.table-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-secondary);
}

.table-container h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 1.5rem;
}

.table {
    margin-bottom: 0;
}

.table th {
    background-color: var(--light-color);
    border: none;
    font-weight: 600;
    color: var(--text-primary);
    padding: 1rem;
}

.table td {
    border: none;
    padding: 1rem;
    vertical-align: middle;
}

.table tbody tr {
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    transition: background-color 0.2s ease;
}

.table tbody tr:hover {
    background-color: rgba(255, 255, 0, 0.05);
}

/* Buttons */
.btn-primary {
    background: var(--gradient-primary);
    border: none;
    color: var(--secondary-color);
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
    color: var(--secondary-color);
}

.btn-outline-primary {
    border-color: var(--primary-color);
    color: var(--primary-color);
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--secondary-color);
    transform: translateY(-1px);
}

/* Dark Mode Toggle */
.dark-mode-toggle-container {
    display: flex;
    align-items: center;
    margin-left: 15px;
}

.dark-mode-toggle {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.dark-mode-toggle input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: 0.4s;
    border-radius: 24px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: 0.4s;
    border-radius: 50%;
}

.dark-mode-toggle input:checked + .slider {
    background-color: var(--secondary-color);
}

.dark-mode-toggle input:checked + .slider:before {
    transform: translateX(26px);
}

.dark-mode-toggle-label {
    margin-left: 10px;
    color: white;
    font-weight: 500;
}

/* Footer */
footer {
    background: var(--gradient-primary);
    color: white;
    padding: 2rem 0;
    margin-top: auto;
}

footer p {
    margin: 0;
    color: rgba(255, 255, 255, 0.8);
    text-align: center;
}

/* Dark Mode Styles */
body.dark-mode {
    background-color: #121212;
    color: #e0e0e0;
}

.dark-mode .stat-card,
.dark-mode .chart-container,
.dark-mode .table-container,
.dark-mode .quick-action-btn {
    background-color: #1e1e1e;
    border-color: #444;
    color: #e0e0e0;
}

.dark-mode .stat-card .stat-number,
.dark-mode .chart-container h3,
.dark-mode .table-container h3 {
    color: #e0e0e0;
}

.dark-mode .table th {
    background-color: #2d2d2d;
    color: #e0e0e0;
}

.dark-mode .table tbody tr:hover {
    background-color: rgba(255, 255, 0, 0.1);
}

.dark-mode .dark-mode-toggle-label {
    color: #e0e0e0;
}

.dark-mode .btn-outline-primary {
    border-color: var(--secondary-color);
    color: var(--secondary-color);
}

.dark-mode .btn-outline-primary:hover {
    background-color: var(--secondary-color);
    color: var(--primary-color);
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-section h1 {
        font-size: 2rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .quick-actions {
        flex-direction: column;
        align-items: center;
    }

    .quick-action-btn {
        min-width: 250px;
    }
}


===== .:\Users\azizj\OneDrive\Documents\Aziz\GS\gs_project\gs_app\static\css\home.css =====
/* Modern Professional Design with Black & Yellow Theme */
:root {
    --primary-color: #000000;
    --secondary-color: #ffff00;
    --accent-color: #ffd700;
    --dark-color: #1f1f1f;
    --light-color: #f8fafc;
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --gradient-primary: linear-gradient(135deg, #000000 0%, #333333 100%);
    --gradient-secondary: linear-gradient(135deg, #ffff00 0%, #ffd700 100%);
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--light-color);
    overflow-x: hidden;
}

/* Navigation */
.navbar {
    background: var(--primary-color) !important;
    backdrop-filter: blur(10px);
    box-shadow: var(--shadow-md);
    padding: 1rem 0;
    transition: all 0.3s ease;
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
    color: var(--secondary-color) !important;
    text-decoration: none;
}

.nav-link {
    color: rgba(255, 255, 255, 0.9) !important;
    font-weight: 500;
    padding: 0.5rem 1rem !important;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
    position: relative;
}

.nav-link:hover {
    color: var(--secondary-color) !important;
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateY(-1px);
}

/* Hero Section */
.hero-section {
    background: var(--gradient-primary);
    color: white;
    padding: 120px 0 80px;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 100" fill="rgba(255,255,0,0.05)"><polygon points="1000,100 1000,0 0,100"/></svg>');
    background-size: cover;
}

.hero-content {
    position: relative;
    z-index: 2;
}

.hero-section h1 {
    font-size: 3.5rem;
    font-weight: 800;
    margin-bottom: 1.5rem;
    background: linear-gradient(45deg, #ffffff, var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-section p {
    font-size: 1.25rem;
    margin-bottom: 2rem;
    color: rgba(255, 255, 255, 0.9);
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.hero-section img {
    max-width: 350px;
    height: auto;
    margin: 2rem 0;
    border-radius: 1rem;
    box-shadow: var(--shadow-xl);
    transition: transform 0.3s ease;
}

.hero-section img:hover {
    transform: scale(1.05);
}

.btn-hero {
    background: var(--gradient-secondary);
    border: none;
    color: var(--primary-color);
    font-weight: 700;
    padding: 1rem 2.5rem;
    border-radius: 50px;
    font-size: 1.1rem;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-lg);
}

.btn-hero:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
    color: var(--primary-color);
    background: linear-gradient(135deg, #ffd700 0%, #ffff00 100%);
}

/* Info Section */
.info-section {
    padding: 100px 0;
    background: linear-gradient(180deg, var(--light-color) 0%, #ffffff 100%);
}

.section-title {
    text-align: center;
    margin-bottom: 4rem;
}

.section-title h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.section-title p {
    font-size: 1.1rem;
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
}

/* Cards */
.feature-card {
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    height: 100%;
    box-shadow: var(--shadow-sm);
    border: 1px solid rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-secondary);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
}

.feature-card:hover::before {
    transform: scaleX(1);
}

.feature-card img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    border-radius: 0.75rem;
    margin-bottom: 1.5rem;
    transition: transform 0.3s ease;
}

.feature-card:hover img {
    transform: scale(1.05);
}

.feature-card h5 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.feature-card p {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: 0;
}

/* Modern Chat Interface Styles */
#chat-container {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 350px;
    z-index: 1000;
    box-shadow: var(--shadow-xl);
    border-radius: 1rem;
    overflow: hidden;
    font-family: 'Inter', sans-serif;
    background: white;
}

#chat-header {
    background: var(--gradient-primary);
    color: white;
    padding: 1rem 1.25rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
}

#chat-header h5 {
    margin: 0;
    font-size: 1rem;
    font-weight: 600;
    color: var(--secondary-color);
}

#toggle-chat {
    background: none;
    border: none;
    color: var(--secondary-color);
    font-size: 1.25rem;
    cursor: pointer;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.2s ease;
}

#toggle-chat:hover {
    background-color: rgba(255, 255, 0, 0.1);
}

#chat-messages {
    height: 300px;
    background: white;
    overflow-y: auto;
    padding: 1rem;
}

.message {
    margin-bottom: 1rem;
    max-width: 85%;
    word-wrap: break-word;
}

.user-message {
    margin-left: auto;
    text-align: right;
}

.user-message span {
    background: var(--gradient-primary);
    color: var(--secondary-color);
    padding: 0.75rem 1rem;
    border-radius: 1.25rem 1.25rem 0.25rem 1.25rem;
    display: inline-block;
    font-size: 0.9rem;
    box-shadow: var(--shadow-sm);
}

.bot-message {
    margin-right: auto;
    text-align: left;
}

.bot-message span {
    background: #f1f5f9;
    color: var(--text-primary);
    padding: 0.75rem 1rem;
    border-radius: 1.25rem 1.25rem 1.25rem 0.25rem;
    display: inline-block;
    font-size: 0.9rem;
    border: 1px solid #e2e8f0;
}

#chat-input-container {
    background: #f8fafc;
    padding: 1rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

#chat-input {
    flex: 1;
    padding: 0.75rem 1rem;
    border: 1px solid #e2e8f0;
    border-radius: 1.5rem;
    outline: none;
    font-size: 0.9rem;
    transition: border-color 0.2s ease;
}

#chat-input:focus {
    border-color: var(--primary-color);
}

#send-button {
    background: var(--gradient-primary);
    color: var(--secondary-color);
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: transform 0.2s ease;
    box-shadow: var(--shadow-sm);
}

#send-button:hover {
    transform: scale(1.05);
}

.typing-indicator {
    color: var(--text-secondary);
    font-style: italic;
    margin: 0.5rem 0;
    font-size: 0.85rem;
}

/* Footer */
.footer {
    background: var(--gradient-primary);
    color: white;
    padding: 4rem 0 2rem;
    margin-top: 5rem;
}

.footer h5 {
    font-weight: 600;
    margin-bottom: 1.5rem;
    color: var(--secondary-color);
}

.footer p, .footer a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer a:hover {
    color: var(--secondary-color);
}

.footer .btn-outline-light {
    border-color: var(--secondary-color);
    color: var(--secondary-color);
    transition: all 0.3s ease;
}

.footer .btn-outline-light:hover {
    background-color: var(--secondary-color);
    color: var(--primary-color);
    border-color: var(--secondary-color);
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-section h1 {
        font-size: 2.5rem;
    }
    
    .hero-section p {
        font-size: 1.1rem;
    }
    
    .section-title h2 {
        font-size: 2rem;
    }
    
    #chat-container {
        width: 300px;
        bottom: 10px;
        right: 10px;
    }
}


===== .:\Users\azizj\OneDrive\Documents\Aziz\GS\gs_project\gs_app\static\css\orders.css =====
/* Orders Page Professional Design with Black & Yellow Theme */
:root {
    --primary-color: #000000;
    --secondary-color: #ffff00;
    --accent-color: #ffd700;
    --dark-color: #1f1f1f;
    --light-color: #f8fafc;
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --gradient-primary: linear-gradient(135deg, #000000 0%, #333333 100%);
    --gradient-secondary: linear-gradient(135deg, #ffff00 0%, #ffd700 100%);
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--light-color);
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    transition: all 0.3s ease;
}

/* Navigation */
.navbar {
    background: var(--gradient-primary) !important;
    backdrop-filter: blur(10px);
    box-shadow: var(--shadow-md);
    padding: 1rem 0;
    transition: all 0.3s ease;
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
    color: var(--secondary-color) !important;
    text-decoration: none;
}

.nav-link {
    color: rgba(255, 255, 255, 0.9) !important;
    font-weight: 500;
    padding: 0.5rem 1rem !important;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
    position: relative;
}

.nav-link:hover {
    color: var(--secondary-color) !important;
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateY(-1px);
}

.nav-link.active {
    background-color: rgba(255, 255, 0, 0.2) !important;
    color: var(--secondary-color) !important;
}

/* Header Section */
.header-section {
    background: var(--gradient-primary);
    color: white;
    padding: 4rem 0 3rem;
    margin-bottom: 2rem;
    position: relative;
    overflow: hidden;
}

.header-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 100" fill="rgba(255,255,0,0.05)"><polygon points="1000,100 1000,0 0,100"/></svg>');
    background-size: cover;
}

.header-content {
    position: relative;
    z-index: 2;
}

.header-section h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    background: linear-gradient(45deg, #ffffff, var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.header-section p {
    font-size: 1.1rem;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 0;
}

/* Main Content */
.main-container {
    flex: 1 0 auto;
    padding: 2rem 0;
}

.order-list {
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: var(--shadow-md);
    border: 1px solid rgba(0, 0, 0, 0.05);
    position: relative;
    overflow: hidden;
}

.order-list::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-secondary);
}

.order-list h2 {
    font-size: 1.75rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 1.5rem;
}

/* Order Items */
.order-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    border-radius: 0.5rem;
    margin-bottom: 0.5rem;
}

.order-item:hover {
    background-color: rgba(255, 255, 0, 0.05);
    transform: translateX(4px);
    box-shadow: var(--shadow-sm);
}

.order-item:last-child {
    border-bottom: none;
}

.order-info strong {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
}

.order-info .text-muted {
    color: var(--text-secondary) !important;
    font-weight: 500;
}

.order-info div:last-child {
    margin-top: 0.5rem;
    font-size: 0.9rem;
    color: var(--text-secondary);
}

/* Order Actions */
.order-actions {
    display: flex;
    gap: 0.5rem;
}

/* Buttons */
.btn-primary {
    background: var(--gradient-primary);
    border: none;
    color: var(--secondary-color);
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
    color: var(--secondary-color);
}

.btn-outline-primary {
    border-color: var(--primary-color);
    color: var(--primary-color);
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--secondary-color);
    transform: translateY(-1px);
}

.btn-outline-danger {
    border-color: #dc3545;
    color: #dc3545;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-outline-danger:hover {
    background-color: #dc3545;
    border-color: #dc3545;
    color: white;
    transform: translateY(-1px);
}

.btn-outline-info {
    border-color: #17a2b8;
    color: #17a2b8;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-outline-info:hover {
    background-color: #17a2b8;
    border-color: #17a2b8;
    color: white;
    transform: translateY(-1px);
}

/* Modals */
.modal-content {
    border-radius: 1rem;
    border: none;
    box-shadow: var(--shadow-xl);
}

.modal-header {
    background: var(--gradient-primary);
    color: white;
    border-radius: 1rem 1rem 0 0;
    border-bottom: none;
}

.modal-header .modal-title {
    font-weight: 600;
    color: var(--secondary-color);
}

.modal-header .btn-close {
    filter: brightness(0) invert(1);
}

.modal-body {
    padding: 2rem;
}

.modal-footer {
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    padding: 1.5rem 2rem;
}

/* Form Styling */
.modal-body form input,
.modal-body form select,
.modal-body form textarea {
    width: 100%;
    padding: 0.75rem 1rem;
    font-size: 1rem;
    line-height: 1.5;
    color: var(--text-primary);
    background-color: #fff;
    border: 2px solid #e2e8f0;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
}

.modal-body form input:focus,
.modal-body form select:focus,
.modal-body form textarea:focus {
    border-color: var(--primary-color);
    outline: 0;
    box-shadow: 0 0 0 0.25rem rgba(0, 0, 0, 0.1);
}

/* Dark Mode Toggle */
.dark-mode-toggle-container {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.dark-mode-toggle {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.dark-mode-toggle input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: 0.4s;
    border-radius: 24px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: 0.4s;
    border-radius: 50%;
}

.dark-mode-toggle input:checked + .slider {
    background-color: var(--secondary-color);
}

.dark-mode-toggle input:checked + .slider:before {
    transform: translateX(26px);
}

.dark-mode-toggle-label {
    color: white;
    font-weight: 500;
}

/* Alerts */
.alert-info {
    background-color: rgba(255, 255, 0, 0.1);
    border-color: var(--accent-color);
    color: var(--text-primary);
    border-radius: 0.5rem;
}

/* Footer */
footer {
    flex-shrink: 0;
    background: var(--gradient-primary);
    color: white;
    padding: 2rem 0;
    text-align: center;
}

footer p {
    margin: 0;
    color: rgba(255, 255, 255, 0.8);
}

/* Dark Mode Styles */
html.dark-mode,
html.dark-mode body {
    background-color: #121212 !important;
    color: #e0e0e0;
}

html.dark-mode .main-container {
    background-color: #121212 !important;
}

html.dark-mode .container {
    background-color: #121212 !important;
}

html.dark-mode .row {
    background-color: #121212 !important;
}

html.dark-mode .col-md-10 {
    background-color: #121212 !important;
}

html.dark-mode * {
    background-color: inherit;
}

html.dark-mode .order-list {
    background-color: #1e1e1e !important;
    border-color: #444;
    color: #e0e0e0;
}

html.dark-mode .order-list h2 {
    color: #e0e0e0;
}

html.dark-mode .order-item {
    border-bottom-color: #333;
}

html.dark-mode .order-item:hover {
    background-color: rgba(255, 255, 0, 0.1);
}

html.dark-mode .order-info strong {
    color: #e0e0e0;
}

html.dark-mode .text-muted {
    color: #a0a0a0 !important;
}

html.dark-mode .modal-content {
    background-color: #1e1e1e;
    color: #e0e0e0;
}

.dark-mode .modal-body form input,
.dark-mode .modal-body form select,
.dark-mode .modal-body form textarea {
    background-color: #2c2c2c;
    border-color: #444;
    color: #e0e0e0;
}

.dark-mode .modal-body form input:focus,
.dark-mode .modal-body form select:focus,
.dark-mode .modal-body form textarea:focus {
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 0.25rem rgba(255, 255, 0, 0.25);
}

.dark-mode .dark-mode-toggle-label {
    color: #e0e0e0;
}

.dark-mode .btn-outline-primary {
    border-color: var(--secondary-color);
    color: var(--secondary-color);
}

.dark-mode .btn-outline-primary:hover {
    background-color: var(--secondary-color);
    color: var(--primary-color);
}

.dark-mode .btn-outline-info {
    border-color: #66cdaa;
    color: #66cdaa;
}

.dark-mode .btn-outline-info:hover {
    background-color: #17a2b8;
    color: white;
}

.dark-mode .btn-outline-danger {
    border-color: #ff6666;
    color: #ff6666;
}

.dark-mode .btn-outline-danger:hover {
    background-color: #dc3545;
    color: white;
}

.dark-mode .alert-info {
    background-color: rgba(255, 255, 0, 0.15);
    border-color: var(--accent-color);
    color: #e0e0e0;
}

.dark-mode footer {
    background: var(--gradient-primary);
    color: #e0e0e0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-section h1 {
        font-size: 2rem;
    }

    .order-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .order-actions {
        width: 100%;
        justify-content: flex-end;
    }
}


===== .:\Users\azizj\OneDrive\Documents\Aziz\GS\gs_project\gs_app\static\css\products.css =====
/* Products Page Professional Design with Black & Yellow Theme */
:root {
    --primary-color: #000000;
    --secondary-color: #ffff00;
    --accent-color: #ffd700;
    --dark-color: #1f1f1f;
    --light-color: #f8fafc;
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --gradient-primary: linear-gradient(135deg, #000000 0%, #333333 100%);
    --gradient-secondary: linear-gradient(135deg, #ffff00 0%, #ffd700 100%);
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--light-color);
    transition: all 0.3s ease;
}

/* Navigation */
.navbar {
    background: var(--gradient-primary) !important;
    backdrop-filter: blur(10px);
    box-shadow: var(--shadow-md);
    padding: 1rem 0;
    transition: all 0.3s ease;
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
    color: var(--secondary-color) !important;
    text-decoration: none;
}

.nav-link {
    color: rgba(255, 255, 255, 0.9) !important;
    font-weight: 500;
    padding: 0.5rem 1rem !important;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
    position: relative;
}

.nav-link:hover {
    color: var(--secondary-color) !important;
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateY(-1px);
}

.nav-link.active {
    background-color: rgba(255, 255, 0, 0.2) !important;
    color: var(--secondary-color) !important;
}

/* Header Section */
.header-section {
    background: var(--gradient-primary);
    color: white;
    padding: 4rem 0 3rem;
    margin-bottom: 2rem;
    position: relative;
    overflow: hidden;
}

.header-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 100" fill="rgba(255,255,0,0.05)"><polygon points="1000,100 1000,0 0,100"/></svg>');
    background-size: cover;
}

.header-content {
    position: relative;
    z-index: 2;
}

.header-section h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    background: linear-gradient(45deg, #ffffff, var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.header-section p {
    font-size: 1.1rem;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 0;
}

/* Main Content */
.main-container {
    padding: 2rem 0;
}

.product-list {
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: var(--shadow-md);
    border: 1px solid rgba(0, 0, 0, 0.05);
    position: relative;
    overflow: hidden;
}

.product-list::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-secondary);
}

.product-list h2 {
    font-size: 1.75rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 1.5rem;
}

/* Product Items */
.product-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    border-radius: 0.5rem;
    margin-bottom: 0.5rem;
}

.product-item:hover {
    background-color: rgba(255, 255, 0, 0.05);
    transform: translateX(4px);
    box-shadow: var(--shadow-sm);
}

.product-item:last-child {
    border-bottom: none;
}

.product-info strong {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
}

.product-info .text-muted {
    color: var(--text-secondary) !important;
    font-weight: 500;
}

.product-info div:last-child {
    margin-top: 0.5rem;
    font-size: 0.9rem;
    color: var(--text-secondary);
}

/* Product Actions */
.product-actions {
    display: flex;
    gap: 0.5rem;
}

/* Buttons */
.btn-primary {
    background: var(--gradient-primary);
    border: none;
    color: var(--secondary-color);
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
    color: var(--secondary-color);
}

.btn-outline-primary {
    border-color: var(--primary-color);
    color: var(--primary-color);
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--secondary-color);
    transform: translateY(-1px);
}

.btn-outline-danger {
    border-color: #dc3545;
    color: #dc3545;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-outline-danger:hover {
    background-color: #dc3545;
    border-color: #dc3545;
    color: white;
    transform: translateY(-1px);
}

/* Modals */
.modal-content {
    border-radius: 1rem;
    border: none;
    box-shadow: var(--shadow-xl);
}

.modal-header {
    background: var(--gradient-primary);
    color: white;
    border-radius: 1rem 1rem 0 0;
    border-bottom: none;
}

.modal-header .modal-title {
    font-weight: 600;
    color: var(--secondary-color);
}

.modal-header .btn-close {
    filter: brightness(0) invert(1);
}

.modal-body {
    padding: 2rem;
}

.modal-footer {
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    padding: 1.5rem 2rem;
}

/* Form Styling */
.modal-body form p {
    display: flex;
    flex-direction: column;
    margin-bottom: 1.5rem;
}

.modal-body form label {
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
}

.modal-body form input,
.modal-body form select,
.modal-body form textarea {
    width: 100%;
    padding: 0.75rem 1rem;
    font-size: 1rem;
    line-height: 1.5;
    color: var(--text-primary);
    background-color: #fff;
    border: 2px solid #e2e8f0;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
}

.modal-body form input:focus,
.modal-body form select:focus,
.modal-body form textarea:focus {
    border-color: var(--primary-color);
    outline: 0;
    box-shadow: 0 0 0 0.25rem rgba(0, 0, 0, 0.1);
}

.modal-body form .helptext {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin-top: 0.5rem;
}

/* Dark Mode Toggle */
.dark-mode-toggle-container {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.dark-mode-toggle {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.dark-mode-toggle input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: 0.4s;
    border-radius: 24px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: 0.4s;
    border-radius: 50%;
}

.dark-mode-toggle input:checked + .slider {
    background-color: var(--secondary-color);
}

.dark-mode-toggle input:checked + .slider:before {
    transform: translateX(26px);
}

.fs-sm {
    color: white;
    font-weight: 500;
}

/* Alerts */
.alert-info {
    background-color: rgba(255, 255, 0, 0.1);
    border-color: var(--accent-color);
    color: var(--text-primary);
    border-radius: 0.5rem;
}

/* Dark Mode Styles */
html.dark-mode body {
    background-color: #121212;
    color: #e0e0e0;
}

html.dark-mode .product-list {
    background-color: #1e1e1e !important;
    border-color: #444;
    color: #e0e0e0;
}

html.dark-mode .product-list h2 {
    color: #e0e0e0;
}

html.dark-mode .product-item {
    border-bottom-color: #333;
}

html.dark-mode .product-item:hover {
    background-color: rgba(255, 255, 0, 0.1);
}

html.dark-mode .product-info strong {
    color: #e0e0e0;
}

html.dark-mode .text-muted {
    color: #a0a0a0 !important;
}

html.dark-mode .modal-content {
    background-color: #1e1e1e;
    color: #e0e0e0;
}

html.dark-mode .modal-body form input,
html.dark-mode .modal-body form select,
html.dark-mode .modal-body form textarea {
    background-color: #2c2c2c;
    border-color: #444;
    color: #e0e0e0;
}

html.dark-mode .modal-body form input:focus,
html.dark-mode .modal-body form select:focus,
html.dark-mode .modal-body form textarea:focus {
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 0.25rem rgba(255, 255, 0, 0.25);
}

html.dark-mode .modal-body form label {
    color: #e0e0e0;
}

html.dark-mode .modal-body form .helptext {
    color: #adb5bd;
}

html.dark-mode .fs-sm {
    color: #e0e0e0;
}

html.dark-mode .btn-outline-primary {
    border-color: var(--secondary-color);
    color: var(--secondary-color);
}

html.dark-mode .btn-outline-primary:hover {
    background-color: var(--secondary-color);
    color: var(--primary-color);
}

html.dark-mode .alert-info {
    background-color: rgba(255, 255, 0, 0.15);
    border-color: var(--accent-color);
    color: #e0e0e0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-section h1 {
        font-size: 2rem;
    }

    .product-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .product-actions {
        width: 100%;
        justify-content: flex-end;
    }
}


===== .:\Users\azizj\OneDrive\Documents\Aziz\GS\gs_project\gs_app\static\css\suppliers.css =====
/* Suppliers Page Professional Design with Black & Yellow Theme */
:root {
    --primary-color: #000000;
    --secondary-color: #ffff00;
    --accent-color: #ffd700;
    --dark-color: #1f1f1f;
    --light-color: #f8fafc;
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --gradient-primary: linear-gradient(135deg, #000000 0%, #333333 100%);
    --gradient-secondary: linear-gradient(135deg, #ffff00 0%, #ffd700 100%);
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--light-color);
    padding: 20px;
    transition: all 0.3s ease;
}

/* Navigation */
.navbar {
    background: var(--gradient-primary) !important;
    backdrop-filter: blur(10px);
    box-shadow: var(--shadow-md);
    padding: 1rem;
    margin-bottom: 20px;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
    color: var(--secondary-color) !important;
    text-decoration: none;
}

.nav-link {
    color: rgba(255, 255, 255, 0.9) !important;
    font-weight: 500;
    padding: 0.5rem 1rem !important;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
    position: relative;
}

.nav-link:hover {
    color: var(--secondary-color) !important;
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateY(-1px);
}

.nav-link.active {
    background-color: rgba(255, 255, 0, 0.2) !important;
    color: var(--secondary-color) !important;
}

/* Header Section */
.header-section {
    background: var(--gradient-primary);
    color: white;
    padding: 4rem 0 3rem;
    margin-bottom: 2rem;
    border-radius: 1rem;
    position: relative;
    overflow: hidden;
}

.header-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 100" fill="rgba(255,255,0,0.05)"><polygon points="1000,100 1000,0 0,100"/></svg>');
    background-size: cover;
}

.header-content {
    position: relative;
    z-index: 2;
}

.header-section h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    background: linear-gradient(45deg, #ffffff, var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.header-section p {
    font-size: 1.1rem;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 0;
}

/* Main Content */
.supplier-list {
    background: white;
    border-radius: 1rem;
    box-shadow: var(--shadow-md);
    border: 1px solid rgba(0, 0, 0, 0.05);
    position: relative;
    overflow: hidden;
    margin-bottom: 1.5rem;
}

.supplier-list::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-secondary);
}

.card-header {
    background: transparent !important;
    border: none !important;
    padding: 2rem 2rem 1rem;
}

.card-header h2 {
    font-size: 1.75rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0;
}

.card-body {
    padding: 0 2rem 2rem;
}

/* Table Styling */
.table-responsive {
    border-radius: 0.75rem;
    overflow: hidden;
}

.table {
    margin-bottom: 0;
}

.table th {
    background-color: var(--light-color);
    border: none;
    font-weight: 600;
    color: var(--text-primary);
    padding: 1rem;
}

.table td {
    border: none;
    padding: 1rem;
    vertical-align: middle;
}

.table tbody tr {
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    transition: background-color 0.2s ease;
}

.table tbody tr:hover {
    background-color: rgba(255, 255, 0, 0.05);
}

/* Buttons */
.btn-primary {
    background: var(--gradient-primary);
    border: none;
    color: var(--secondary-color);
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
    color: var(--secondary-color);
}

.btn-success {
    background: var(--gradient-secondary);
    border: none;
    color: var(--primary-color);
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
}

.btn-success:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
    color: var(--primary-color);
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
}

.btn-outline-primary {
    border-color: var(--primary-color);
    color: var(--primary-color);
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--secondary-color);
    transform: translateY(-1px);
}

.btn-outline-danger {
    border-color: #dc3545;
    color: #dc3545;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-outline-danger:hover {
    background-color: #dc3545;
    border-color: #dc3545;
    color: white;
    transform: translateY(-1px);
}

/* Modals */
.modal-content {
    border-radius: 1rem;
    border: none;
    box-shadow: var(--shadow-xl);
}

.modal-header {
    background: var(--gradient-primary);
    color: white;
    border-radius: 1rem 1rem 0 0;
    border-bottom: none;
}

.modal-header .modal-title {
    font-weight: 600;
    color: var(--secondary-color);
}

.modal-header .btn-close {
    filter: brightness(0) invert(1);
}

.modal-body {
    padding: 2rem;
}

.modal-footer {
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    padding: 1.5rem 2rem;
}

/* Form Styling */
.form-label {
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
}

.form-control,
.form-select {
    padding: 0.75rem 1rem;
    font-size: 1rem;
    line-height: 1.5;
    color: var(--text-primary);
    background-color: #fff;
    border: 2px solid #e2e8f0;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
}

.form-control:focus,
.form-select:focus {
    border-color: var(--primary-color);
    outline: 0;
    box-shadow: 0 0 0 0.25rem rgba(0, 0, 0, 0.1);
}

.form-text {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin-top: 0.5rem;
}

/* Dark Mode Toggle */
.dark-mode-toggle-container {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.dark-mode-toggle {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.dark-mode-toggle input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: 0.4s;
    border-radius: 24px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: 0.4s;
    border-radius: 50%;
}

.dark-mode-toggle input:checked + .slider {
    background-color: var(--secondary-color);
}

.dark-mode-toggle input:checked + .slider:before {
    transform: translateX(26px);
}

.dark-mode-toggle-label {
    color: white;
    font-weight: 500;
}

/* Footer */
footer {
    background: var(--gradient-primary);
    color: white;
    padding: 2rem 0;
    margin-top: 3rem;
    border-radius: 1rem;
    text-align: center;
}

footer p {
    margin: 0;
    color: rgba(255, 255, 255, 0.8);
}

/* Dark Mode Styles */
html.dark-mode body {
    background-color: #121212;
    color: #e0e0e0;
}

html.dark-mode .supplier-list {
    background-color: #1e1e1e !important;
    border-color: #444;
    color: #e0e0e0;
}

html.dark-mode .card {
    background-color: #1e1e1e !important;
    color: #e0e0e0;
}

html.dark-mode .card-body {
    background-color: #1e1e1e !important;
}

html.dark-mode .card-header h2 {
    color: #e0e0e0;
}

html.dark-mode .table th {
    background-color: #2d2d2d;
    color: #e0e0e0;
}

html.dark-mode .table tbody tr {
    border-bottom-color: #333;
}

html.dark-mode .table tbody tr:hover {
    background-color: rgba(255, 255, 0, 0.1);
}

html.dark-mode .table {
    color: #e0e0e0;
    background-color: transparent;
}

html.dark-mode .table-responsive {
    background-color: #1e1e1e !important;
}

html.dark-mode .table tbody tr {
    background-color: transparent;
}

html.dark-mode .table td {
    background-color: transparent !important;
    color: #e0e0e0 !important;
}

html.dark-mode .table tbody {
    background-color: transparent !important;
}

html.dark-mode .table thead th {
    background-color: #2d2d2d !important;
    color: #e0e0e0 !important;
}

html.dark-mode .modal-content {
    background-color: #1e1e1e;
    color: #e0e0e0;
}

html.dark-mode .form-control,
html.dark-mode .form-select {
    background-color: #2c2c2c;
    border-color: #444;
    color: #e0e0e0;
}

html.dark-mode .form-control:focus,
html.dark-mode .form-select:focus {
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 0.25rem rgba(255, 255, 0, 0.25);
}

html.dark-mode .form-label {
    color: #e0e0e0;
}

html.dark-mode .form-text {
    color: #adb5bd;
}

html.dark-mode .dark-mode-toggle-label {
    color: #e0e0e0;
}

html.dark-mode .btn-outline-primary {
    border-color: var(--secondary-color);
    color: var(--secondary-color);
}

html.dark-mode .btn-outline-primary:hover {
    background-color: var(--secondary-color);
    color: var(--primary-color);
}

html.dark-mode .btn-outline-danger {
    border-color: #ff6666;
    color: #ff6666;
}

html.dark-mode .btn-outline-danger:hover {
    background-color: #dc3545;
    color: white;
}

html.dark-mode .text-danger {
    color: #ff6666 !important;
}


===== .:\Users\azizj\OneDrive\Documents\Aziz\GS\gs_project\gs_app\static\css\upload.css =====
/* Upload Stock Page Professional Design with Black & Yellow Theme */
:root {
    --primary-color: #000000;
    --secondary-color: #ffff00;
    --accent-color: #ffd700;
    --dark-color: #1f1f1f;
    --light-color: #f8fafc;
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --gradient-primary: linear-gradient(135deg, #000000 0%, #333333 100%);
    --gradient-secondary: linear-gradient(135deg, #ffff00 0%, #ffd700 100%);
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--light-color);
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    transition: all 0.3s ease;
}

/* Navigation */
.navbar {
    background: var(--gradient-primary) !important;
    backdrop-filter: blur(10px);
    box-shadow: var(--shadow-md);
    padding: 1rem 0;
    transition: all 0.3s ease;
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
    color: var(--secondary-color) !important;
    text-decoration: none;
}

.nav-link {
    color: rgba(255, 255, 255, 0.9) !important;
    font-weight: 500;
    padding: 0.5rem 1rem !important;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
    position: relative;
}

.nav-link:hover {
    color: var(--secondary-color) !important;
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateY(-1px);
}

.nav-link.active {
    background-color: rgba(255, 255, 0, 0.2) !important;
    color: var(--secondary-color) !important;
}

/* Header Section */
.header-section {
    background: var(--gradient-primary);
    color: white;
    padding: 4rem 0 3rem;
    margin-bottom: 2rem;
    position: relative;
    overflow: hidden;
}

.header-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 100" fill="rgba(255,255,0,0.05)"><polygon points="1000,100 1000,0 0,100"/></svg>');
    background-size: cover;
}

.header-content {
    position: relative;
    z-index: 2;
}

.header-section h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    background: linear-gradient(45deg, #ffffff, var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.header-section p {
    font-size: 1.1rem;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 0;
}

/* Main Content */
.main-container {
    flex: 1 0 auto;
    background: white;
    border-radius: 1rem;
    padding: 3rem;
    box-shadow: var(--shadow-md);
    border: 1px solid rgba(0, 0, 0, 0.05);
    margin: 2rem auto;
    max-width: 800px;
    position: relative;
    overflow: hidden;
}

.main-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-secondary);
}

.main-container h1 {
    color: var(--text-primary);
    margin-bottom: 2rem;
    font-weight: 600;
    text-align: center;
}

/* Form Styling */
.form-group {
    margin-bottom: 2rem;
}

.form-group label {
    font-weight: 600;
    margin-bottom: 0.75rem;
    color: var(--text-primary);
    display: block;
}

.form-control {
    padding: 1rem;
    font-size: 1rem;
    line-height: 1.5;
    color: var(--text-primary);
    background-color: #fff;
    border: 2px solid #e2e8f0;
    border-radius: 0.75rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: var(--primary-color);
    outline: 0;
    box-shadow: 0 0 0 0.25rem rgba(0, 0, 0, 0.1);
}

/* Buttons */
.btn-primary {
    background: var(--gradient-primary);
    border: none;
    color: var(--secondary-color);
    font-weight: 600;
    padding: 1rem 2rem;
    border-radius: 0.75rem;
    transition: all 0.3s ease;
    font-size: 1.1rem;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    color: var(--secondary-color);
}

.w-100 {
    width: 100%;
}

/* Result Section */
.result {
    margin-top: 3rem;
    padding: 2rem;
    background-color: rgba(255, 255, 0, 0.05);
    border-radius: 1rem;
    border: 1px solid var(--accent-color);
}

.result h2 {
    color: var(--text-primary);
    margin-bottom: 1.5rem;
    font-weight: 600;
}

.result pre {
    background-color: #f8fafc;
    padding: 1.5rem;
    border-radius: 0.75rem;
    border: 1px solid #e2e8f0;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    line-height: 1.6;
    color: var(--text-primary);
    white-space: pre-wrap;
    word-wrap: break-word;
    transition: all 0.3s ease;
}

/* Dark Mode Toggle */
.dark-mode-toggle-container {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.dark-mode-toggle {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.dark-mode-toggle input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: 0.4s;
    border-radius: 24px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: 0.4s;
    border-radius: 50%;
}

.dark-mode-toggle input:checked + .slider {
    background-color: var(--secondary-color);
}

.dark-mode-toggle input:checked + .slider:before {
    transform: translateX(26px);
}

.dark-mode-toggle-label {
    color: white;
    font-weight: 500;
}

/* Footer */
footer {
    flex-shrink: 0;
    background: var(--gradient-primary);
    color: white;
    padding: 2rem 0;
    text-align: center;
}

footer p {
    margin: 0;
    color: rgba(255, 255, 255, 0.8);
}

/* Dark Mode Styles */
html.dark-mode,
html.dark-mode body {
    background-color: #121212 !important;
    color: #e0e0e0;
}

html.dark-mode .main-container {
    background-color: #1e1e1e !important;
    border-color: #444;
    color: #e0e0e0;
}

html.dark-mode .main-container h1 {
    color: #e0e0e0;
}

html.dark-mode .form-group label {
    color: #e0e0e0;
}

html.dark-mode .form-control {
    background-color: #2c2c2c;
    border-color: #444;
    color: #e0e0e0;
}

html.dark-mode .form-control:focus {
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 0.25rem rgba(255, 255, 0, 0.25);
}

html.dark-mode .result {
    background-color: rgba(255, 255, 0, 0.1);
    border-color: var(--accent-color);
}

html.dark-mode .result h2 {
    color: #e0e0e0;
}

html.dark-mode .result pre {
    background-color: #2c2c2c;
    border-color: #444;
    color: #e0e0e0;
}

html.dark-mode .dark-mode-toggle-label {
    color: #e0e0e0;
}

html.dark-mode footer {
    background: var(--gradient-primary);
    color: #e0e0e0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-section h1 {
        font-size: 2rem;
    }

    .main-container {
        margin: 1rem;
        padding: 2rem;
    }

    .btn-primary {
        padding: 0.875rem 1.5rem;
        font-size: 1rem;
    }
}


===== .:\Users\azizj\OneDrive\Documents\Aziz\GS\gs_project\gs_app\templates\dashboard.html =====
{% load static %}
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Tableau de Bord | Gestion de Stock</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <link href="{% static 'css/dashboard.css' %}" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <!-- Navigation Bar -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand d-flex align-items-center" href="{% url 'home' %}">
                <i class="bi bi-fuel-pump me-2"></i>
                <span class="fw-bold">SNDP</span>
                <span class="ms-1">Agil</span>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="{% url 'dashboard' %}">
                            <i class="bi bi-speedometer2 me-1"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'order_list' %}">
                            <i class="bi bi-cart3 me-1"></i> Commandes
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'product_list' %}">
                            <i class="bi bi-box-seam me-1"></i> Produits
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'supplier_list' %}">
                            <i class="bi bi-building me-1"></i> Fournisseurs
                        </a>
                    </li>
                </ul>
                <div class="dark-mode-toggle-container">
                    <label class="dark-mode-toggle">
                        <input type="checkbox" id="toggleSwitch">
                        <span class="slider"></span>
                    </label>
                    <span class="dark-mode-toggle-label">Mode Sombre</span>
                </div>
            </div>
        </div>
    </nav>

    <!-- Header Section -->
    <div class="header-section">
        <div class="container">
            <div class="header-content text-center">
                <h1>
                    <i class="bi bi-speedometer2 me-2"></i>
                    Tableau de Bord
                </h1>
                <p>Vue d'ensemble de votre gestion de stock pétrolier</p>
            </div>
        </div>
    </div>

    <!-- Dashboard Content -->
    <div class="container main-container">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div class="d-flex align-items-center">
                <span class="me-2">Dernière mise à jour: {{ now|date:"d M Y H:i" }}</span>
                <button class="btn btn-sm btn-outline-primary" onclick="window.location.reload()">
                    <i class="bi bi-arrow-clockwise"></i> Actualiser
                </button>
            </div>
        </div>

        <!-- Quick Action Buttons -->
        <div class="quick-actions mb-4">
            <a href="{% url 'product_list' %}?filter=low_stock" class="quick-action-btn text-decoration-none text-warning">
                <i class="bi bi-exclamation-triangle"></i>
                <span>Stock Faible</span>
            </a>
        </div>

        <!-- Summary Cards -->
        <div class="stats-grid">
            <div class="stat-card">
                <i class="stat-icon bi bi-box-seam text-primary"></i>
                <div class="stat-number">{{ total_products }}</div>
                <div class="stat-label">Total des Produits</div>
                <div class="d-flex justify-content-between align-items-center mt-3">
                    <small class="text-success">
                        <i class="bi bi-check-circle-fill"></i> {{ products_in_stock }} en stock
                    </small>
                    <small class="text-danger">
                        <i class="bi bi-x-circle-fill"></i> {{ out_of_stock }} en rupture
                    </small>
                </div>
            </div>
            <div class="stat-card">
                <i class="stat-icon bi bi-cart3 text-success"></i>
                <div class="stat-number">{{ total_orders }}</div>
                <div class="stat-label">Total des Commandes</div>
                <div class="progress mt-3">
                    <div class="progress-bar bg-success" role="progressbar" style="width: 75%" aria-valuenow="75" aria-valuemin="0" aria-valuemax="100"></div>
                </div>
                <small class="text-muted mt-2 d-block">+15% par rapport au mois dernier</small>
            </div>
            <div class="stat-card">
                <i class="stat-icon bi bi-building text-info"></i>
                <div class="stat-number">{{ total_suppliers }}</div>
                <div class="stat-label">Fournisseurs</div>
                <div class="mt-3">
                    <span class="badge bg-primary">Actifs: {{ total_suppliers }}</span>
                </div>
            </div>
            <div class="stat-card">
                <i class="stat-icon bi bi-currency-euro text-warning"></i>
                <div class="stat-number">{{ total_revenue }} €</div>
                <div class="stat-label">Revenu Total</div>
                <div class="progress mt-3">
                    <div class="progress-bar bg-warning" role="progressbar" style="width: 65%" aria-valuenow="65" aria-valuemin="0" aria-valuemax="100"></div>
                </div>
                <small class="text-muted mt-2 d-block">Objectif: 100,000 €</small>
            </div>
        </div>

        <!-- Charts Section -->
        <div class="charts-section">
            <div class="row">
                <div class="col-lg-8 mb-4">
                    <div class="chart-container">
                        <h3>Commandes (6 derniers mois)</h3>
                        <div class="d-flex justify-content-end mb-3">
                            <div class="btn-group btn-group-sm" role="group">
                                <button type="button" class="btn btn-outline-secondary active">6 Mois</button>
                                <button type="button" class="btn btn-outline-secondary">1 An</button>
                            </div>
                        </div>
                        <canvas id="ordersChart"></canvas>
                    </div>
                </div>
                <div class="col-lg-4 mb-4">
                    <div class="chart-container">
                        <h3>Top 5 Produits Vendus</h3>
                        <canvas id="productsChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Low Stock & Recent Orders -->
        <div class="row">
            <div class="col-lg-6 mb-4">
                <div class="table-container">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h3><i class="bi bi-exclamation-triangle text-warning me-2"></i>Produits à Stock Faible</h3>
                        <a href="{% url 'product_list' %}?filter=low_stock" class="btn btn-sm btn-outline-primary">Voir Tous</a>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Produit</th>
                                    <th>Stock Actuel</th>
                                    <th>Stock Min</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for product in low_stock_products %}
                                <tr>
                                    <td>{{ product.name }}</td>
                                    <td>
                                        <span class="badge bg-danger">{{ product.quantity_in_stock }}</span>
                                    </td>
                                    <td>{{ product.min_stock_level }}</td>
                                    <td>
                                        <a href="{% url 'product_list' %}" class="btn btn-sm btn-outline-primary">
                                            <i class="bi bi-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="4" class="text-center py-3">Aucun produit à stock faible</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 mb-4">
                <div class="table-container">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h3><i class="bi bi-clock-history text-info me-2"></i>Commandes Récentes</h3>
                        <a href="{% url 'order_list' %}" class="btn btn-sm btn-outline-primary">Voir Toutes</a>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>Date</th>
                                    <th>Statut</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for order in recent_orders %}
                                <tr>
                                    <td>{{ order.id }}</td>
                                    <td>{{ order.date|date:"d/m/Y" }}</td>
                                    <td>
                                        {% if order.status == 'pending' %}
                                            <span class="badge bg-warning">{{ order.status|title }}</span>
                                        {% elif order.status == 'completed' %}
                                            <span class="badge bg-success">{{ order.status|title }}</span>
                                        {% else %}
                                            <span class="badge bg-secondary">{{ order.status|title }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <a href="{% url 'edit_order' order.id %}" class="btn btn-sm btn-outline-secondary">
                                            <i class="bi bi-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="4" class="text-center py-3">Aucune commande récente.</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer>
        <p>© 2025 Gestion des Commandes - Tous droits réservés.</p>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Dark Mode Toggle
        const html = document.documentElement;
        const toggleSwitch = document.getElementById('toggleSwitch');

        // Load dark mode preference
        if (localStorage.getItem('darkMode') === 'true') {
            html.classList.add('dark-mode');
            toggleSwitch.checked = true;
        }

        // Toggle dark mode
        toggleSwitch.addEventListener('change', () => {
            html.classList.toggle('dark-mode');
            localStorage.setItem('darkMode', html.classList.contains('dark-mode'));
        });

        // Initialize Charts
        document.addEventListener('DOMContentLoaded', function() {
            try {
                // Parse the data from Django context
                const ordersLabels = JSON.parse('{{ orders_labels|safe }}');
                const ordersValues = JSON.parse('{{ orders_values|safe }}');
                const topProductsLabels = JSON.parse('{{ top_products_labels|safe }}');
                const topProductsValues = JSON.parse('{{ top_products_values|safe }}');

                console.log('Orders Labels:', ordersLabels);
                console.log('Orders Values:', ordersValues);
                console.log('Top Products Labels:', topProductsLabels);
                console.log('Top Products Values:', topProductsValues);

                // Function to update chart colors based on dark mode
                function updateChartColors() {
                    const isDarkMode = document.documentElement.classList.contains('dark-mode');
                    const textColor = isDarkMode ? '#e0e0e0' : '#212529';

                    // Orders Chart
                    const ordersChart = new Chart(document.getElementById('ordersChart'), {
                        type: 'line',
                        data: {
                            labels: ordersLabels,
                            datasets: [{
                                label: 'Commandes',
                                data: ordersValues,
                                borderColor: '#007bff',
                                backgroundColor: 'rgba(0, 123, 255, 0.1)',
                                fill: true,
                                tension: 0.4
                            }]
                        },
                        options: {
                            responsive: true,
                            plugins: {
                                legend: {
                                    display: true,
                                    labels: {
                                        color: textColor
                                    }
                                },
                                tooltip: {
                                    mode: 'index',
                                    intersect: false
                                }
                            },
                            scales: {
                                x: {
                                    ticks: {
                                        color: textColor
                                    },
                                    grid: {
                                        color: isDarkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)'
                                    }
                                },
                                y: {
                                    beginAtZero: true,
                                    ticks: {
                                        color: textColor
                                    },
                                    grid: {
                                        color: isDarkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)'
                                    }
                                }
                            }
                        }
                    });

                    // Products Chart
                    const productsChart = new Chart(document.getElementById('productsChart'), {
                        type: 'bar',
                        data: {
                            labels: topProductsLabels,
                            datasets: [{
                                label: 'Quantité Vendue',
                                data: topProductsValues,
                                backgroundColor: [
                                    'rgba(255, 99, 132, 0.7)',
                                    'rgba(54, 162, 235, 0.7)',
                                    'rgba(255, 206, 86, 0.7)',
                                    'rgba(75, 192, 192, 0.7)',
                                    'rgba(153, 102, 255, 0.7)'
                                ],
                                borderColor: [
                                    'rgba(255, 99, 132, 1)',
                                    'rgba(54, 162, 235, 1)',
                                    'rgba(255, 206, 86, 1)',
                                    'rgba(75, 192, 192, 1)',
                                    'rgba(153, 102, 255, 1)'
                                ],
                                borderWidth: 1
                            }]
                        },
                        options: {
                            responsive: true,
                            plugins: {
                                legend: {
                                    display: true,
                                    labels: {
                                        color: textColor
                                    }
                                }
                            },
                            scales: {
                                x: {
                                    ticks: {
                                        color: textColor
                                    },
                                    grid: {
                                        color: isDarkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)'
                                    }
                                },
                                y: {
                                    beginAtZero: true,
                                    ticks: {
                                        color: textColor
                                    },
                                    grid: {
                                        color: isDarkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)'
                                    }
                                }
                            }
                        }
                    });

                    // Update charts when dark mode changes
                    toggleSwitch.addEventListener('change', function() {
                        // Destroy and recreate charts with new colors
                        ordersChart.destroy();
                        productsChart.destroy();
                        updateChartColors();
                    });
                }

                // Initialize charts
                updateChartColors();
            } catch (error) {
                console.error('Error initializing charts:', error);
                // Display a user-friendly error message
                document.getElementById('ordersChart').getContext('2d').clearRect(0, 0,
                    document.getElementById('ordersChart').width,
                    document.getElementById('ordersChart').height);
                document.getElementById('productsChart').getContext('2d').clearRect(0, 0,
                    document.getElementById('productsChart').width,
                    document.getElementById('productsChart').height);

                const errorMessage = document.createElement('div');
                errorMessage.className = 'alert alert-danger mt-3';
                errorMessage.textContent = 'Impossible de charger les graphiques. Veuillez rafraîchir la page.';

                document.getElementById('ordersChart').parentNode.appendChild(errorMessage.cloneNode(true));
                document.getElementById('productsChart').parentNode.appendChild(errorMessage.cloneNode(true));
            }
        });
    </script>
</body>
</html>


===== .:\Users\azizj\OneDrive\Documents\Aziz\GS\gs_project\gs_app\templates\home.html =====
{% load static %}
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SNDP Agil - Gestion de Stock Pétrolier</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="{% static 'css/home.css' %}" rel="stylesheet">


</head>
<body>

<nav class="navbar navbar-expand-lg navbar-dark">
    <div class="container">
        <a class="navbar-brand" href="{% url 'home' %}">SNDP Agil</a>
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav ms-auto">
                <li class="nav-item"><a class="nav-link" href="{% url 'product_list' %}">Produits </a></li>
                <li class="nav-item"><a class="nav-link" href="{% url 'order_list' %}">Commandes</a></li>
                <li class="nav-item"><a class="nav-link" href="{% url 'supplier_list' %}">Fournisseurs</a></li>
                <li class="nav-item"><a class="nav-link" href="{% url 'upload_excel' %}">Ask AI</a></li>

                {% if user.is_superuser %}
                    <li class="nav-item"><a class="nav-link" href="{% url 'admin:index' %}">Admin</a></li>
                {% endif %}

                <li class="nav-item">
                    {% if user.is_authenticated %}
                        <form method="post" action="{% url 'logout' %}" class="d-inline">
                            {% csrf_token %}
                            <button type="submit" class="nav-link btn btn-link" style="color: #fff !important; padding: 0.5rem 1rem; margin: 0; border: none; background: none; text-decoration: none;">
                                Déconnexion
                            </button>
                        </form>
                    {% else %}
                        <a class="nav-link" href="{% url 'login' %}">Connexion</a>
                    {% endif %}
                </li>
            </ul>
        </div>
    </div>
</nav>

    <!-- Hero Section with Main Image -->
    <div class="hero-section">
        <div class="container">
            <div class="hero-content">
                <h1>Bienvenue, {{ user.username }} !</h1>
                <p>Optimisez la gestion de vos stocks pétroliers avec une solution moderne et intuitive</p>
                <img src="{% static '1.png' %}" alt="SNDP Agile" class="img-fluid">
                <br>
                <a href="{% url 'dashboard' %}" class="btn-hero">Accéder au Dashboard</a>
            </div>
        </div>
    </div>

    <!-- Info Section with Cards -->
    <div class="info-section">
        <div class="container">
            <div class="section-title">
                <h2>Solutions de Gestion Avancées</h2>
                <p>Découvrez nos fonctionnalités conçues pour optimiser votre gestion de stock pétrolier</p>
            </div>
            <div class="row g-4">
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <img src="{% static 'ges.png' %}" alt="Gestion Efficace">
                        <h5>Gestion Efficace</h5>
                        <p>Optimisez vos stocks de produits pétroliers avec des outils intelligents qui réduisent les coûts et améliorent l'efficacité opérationnelle.</p>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <img src="{% static 'suivi.png' %}" alt="Suivi en Temps Réel">
                        <h5>Suivi en Temps Réel</h5>
                        <p>Surveillez vos stocks de carburants et autres produits pétroliers en temps réel avec des tableaux de bord interactifs et des alertes automatiques.</p>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <img src="{% static 'rapport.png' %}" alt="Rapports Personnalisés">
                        <h5>Rapports Personnalisés</h5>
                        <p>Générez des rapports détaillés et des analyses approfondies pour une meilleure prise de décision dans la gestion de vos stocks pétroliers.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <!-- Contact Information -->
                <div class="col-lg-4 col-md-6 mb-4">
                    <h5>Nous Contacter</h5>
                    <p>
                        <strong>Téléphone :</strong> +216 123 456 789<br>
                        <strong>Email :</strong> <a href="mailto:<EMAIL>"><EMAIL></a>
                    </p>
                </div>

                <!-- Address -->
                <div class="col-lg-4 col-md-6 mb-4">
                    <h5>Notre Adresse</h5>
                    <p>
                        BP 762 Ave Mohamed Ali Akid<br>
                        Tunis 1003, Tunisie
                    </p>
                </div>

                <!-- Links -->
                <div class="col-lg-4 col-md-6 mb-4">
                    <h5>Liens Utiles</h5>
                    <div class="d-flex flex-column gap-2">
                        <a href="{% url 'map' %}" class="btn btn-outline-light">Voir la Carte</a>
                        <a href="{% url 'dashboard' %}" class="btn btn-outline-light">Dashboard</a>
                    </div>
                </div>
            </div>

            <hr style="border-color: rgba(255, 255, 255, 0.2); margin: 2rem 0 1rem;">

            <div class="text-center">
                <p>© 2025 SNDP Agile. Tous droits réservés.</p>
            </div>
        </div>
    </footer>

    <!-- Modern Chat Interface -->
    <div id="chat-container">
        <div id="chat-header">
            <h5>Assistant SNDP Agile</h5>
            <button id="toggle-chat">−</button>
        </div>
        <div id="chat-messages"></div>
        <div id="chat-input-container">
            <input type="text" id="chat-input" placeholder="Tapez votre message..." />
            <button id="send-button">→</button>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const chatContainer = document.getElementById('chat-container');
            const chatMessages = document.getElementById('chat-messages');
            const chatInput = document.getElementById('chat-input');
            const sendButton = document.getElementById('send-button');
            const toggleButton = document.getElementById('toggle-chat');

            let isChatMinimized = false;

            // Helper function to get CSRF token
            function getCookie(name) {
                let cookieValue = null;
                if (document.cookie && document.cookie !== '') {
                    const cookies = document.cookie.split(';');
                    for (let i = 0; i < cookies.length; i++) {
                        const cookie = cookies[i].trim();
                        if (cookie.substring(0, name.length + 1) === (name + '=')) {
                            cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                            break;
                        }
                    }
                }
                return cookieValue;
            }

            // Toggle chat visibility
            toggleButton.addEventListener('click', () => {
                isChatMinimized = !isChatMinimized;
                if (isChatMinimized) {
                    chatMessages.style.display = 'none';
                    document.getElementById('chat-input-container').style.display = 'none';
                    toggleButton.textContent = '+';
                    chatContainer.style.height = 'auto';
                } else {
                    chatMessages.style.display = 'block';
                    document.getElementById('chat-input-container').style.display = 'flex';
                    toggleButton.textContent = '−';
                    chatContainer.style.height = 'auto';
                }
            });

            // Add welcome message
            addBotMessage("Bonjour! Je suis l'assistant SNDP Agile. Comment puis-je vous aider avec la gestion de vos stocks pétroliers?");

            // Send message function
            function sendMessage() {
                const message = chatInput.value.trim();
                if (!message) return;

                // Add user message
                addUserMessage(message);
                chatInput.value = '';

                // Show typing indicator
                const typingIndicator = addTypingIndicator();

                // Send to Django Ollama endpoint
                fetch('{% url "ollama_chat" %}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': getCookie('csrftoken')
                    },
                    body: JSON.stringify({
                        message: message
                    })
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    // Remove typing indicator
                    chatMessages.removeChild(typingIndicator);

                    // Check if there's an error in the response
                    if (data.error) {
                        addBotMessage(`Erreur: ${data.error}`);
                    } else {
                        // Add bot response
                        addBotMessage(data.message.content);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    chatMessages.removeChild(typingIndicator);
                    addBotMessage("Désolé, je ne peux pas répondre pour le moment. Erreur de connexion: " + error.message);
                });
            }

            // Helper functions for messages
            function addUserMessage(text) {
                const messageDiv = document.createElement('div');
                messageDiv.className = 'message user-message';
                messageDiv.innerHTML = `<span>${text}</span>`;
                chatMessages.appendChild(messageDiv);
                chatMessages.scrollTop = chatMessages.scrollHeight;
            }

            function addBotMessage(text) {
                const messageDiv = document.createElement('div');
                messageDiv.className = 'message bot-message';
                messageDiv.innerHTML = `<span>${text}</span>`;
                chatMessages.appendChild(messageDiv);
                chatMessages.scrollTop = chatMessages.scrollHeight;
            }

            function addTypingIndicator() {
                const typingDiv = document.createElement('div');
                typingDiv.className = 'typing-indicator';
                typingDiv.textContent = "L'assistant tape...";
                chatMessages.appendChild(typingDiv);
                chatMessages.scrollTop = chatMessages.scrollHeight;
                return typingDiv;
            }

            // Event listeners
            sendButton.addEventListener('click', sendMessage);
            chatInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') sendMessage();
            });
        });
    </script>
</body>
</html>

===== .:\Users\azizj\OneDrive\Documents\Aziz\GS\gs_project\gs_app\templates\map.html =====
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>My Google Map</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyBAGZDj8MzsboDodMdjrAhjIfSKPQ2vrUo"></script>
    <script>
      function initMap() {
        var location = { lat: 36.8270, lng: 10.1658 };  // BP 762 Ave Mohamed Ali Akid
        var map = new google.maps.Map(document.getElementById('map'), {
          zoom: 15,
          center: location
        });
        var marker = new google.maps.Marker({
          position: location,
          map: map,
          title: 'BP 762 Ave Mohamed Ali Akid, Tunis 1003'
        });
      }
    </script>
</head>
<body onload="initMap()">
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container mt-4">
            <a class="navbar-brand" href="{% url 'home' %}">Home</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item"><a class="nav-link" href="{% url 'order_list' %}">Commandes</a></li>
                    <li class="nav-item"><a class="nav-link" href="{% url 'product_list' %}">Produits</a></li>
                    <li class="nav-item"><a class="nav-link" href="{% url 'supplier_list' %}">Fournisseurs</a></li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-5">
        <h1 class="text-center mb-4">My Destination</h1>
        <div id="map" style="height: 500px; width: 100%;"></div>
    </div>

    <!-- Bootstrap JS (for the navbar toggler to work) -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>


===== .:\Users\azizj\OneDrive\Documents\Aziz\GS\gs_project\gs_app\templates\upload.html =====
{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Stock Advisor</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css">
    <link href="{% static 'css/upload.css' %}" rel="stylesheet">
</head>
<body>
    <!-- Navigation Bar -->
    <nav class="navbar navbar-expand-lg shadow-sm">
        <div class="container">
            <a class="navbar-brand d-flex align-items-center" href="{% url 'home' %}">
                <span class="fw-bold text-primary">SNDP</span>
                <span class="ms-1">Agil</span>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav mx-auto">
                    <li class="nav-item mx-2">
                        <a class="nav-link" href="{% url 'product_list' %}">
                            <i class="bi bi-box me-1"></i>Produits
                        </a>
                    </li>
                    <li class="nav-item mx-2">
                        <a class="nav-link" href="{% url 'order_list' %}">
                            <i class="bi bi-cart me-1"></i>Commandes
                        </a>
                    </li>
                    <li class="nav-item mx-2">
                        <a class="nav-link" href="{% url 'supplier_list' %}">
                            <i class="bi bi-building me-1"></i>Fournisseurs
                        </a>
                    </li>
                    <li class="nav-item mx-2">
                        <a class="nav-link active" href="{% url 'upload_excel' %}">
                            <i class="bi bi-robot me-1"></i>Ask AI
                        </a>
                    </li>
                </ul>
                <div class="dark-mode-toggle-container d-flex align-items-center">
                    <label class="dark-mode-toggle me-2">
                        <input type="checkbox" id="toggleSwitch">
                        <span class="slider"></span>
                    </label>
                    <span class="text-nowrap fs-sm">Mode Sombre</span>
                </div>
            </div>
        </div>
    </nav>

    <!-- Header Section -->
    <div class="header-section">
        <div class="container">
            <div class="header-content text-center">
                <h1>
                    <i class="bi bi-file-earmark-excel me-2"></i>
                    Analyse Intelligente de Stock
                </h1>
                <p>Téléchargez votre fichier Excel pour une analyse IA avancée</p>
            </div>
        </div>
    </div>

    <!-- Stock Analysis Form -->
    <div class="container main-container">
        <h1 class="text-center">Upload Excel for Stock Analysis</h1>
        <form method="post" enctype="multipart/form-data">
            {% csrf_token %}
            <div class="form-group mb-3">
                <label for="excel_file">Choose Excel File</label>
                <input type="file" class="form-control" id="excel_file" name="excel_file" required>
            </div>
            <button type="submit" class="btn btn-primary w-100">Analyze</button>
        </form>
        {% if advice %}
            <div class="result">
                <h2>AI Advice:</h2>
                <pre>{{ advice }}</pre>
            </div>
        {% endif %}
    </div>

    <!-- Footer -->
    <footer>
        <p>© 2025 Gestion des Commandes - All rights reserved.</p>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Dark Mode Toggle
        const html = document.documentElement;
        const toggleSwitch = document.getElementById('toggleSwitch');

        // Load dark mode preference
        if (localStorage.getItem('darkMode') === 'true') {
            html.classList.add('dark-mode');
            toggleSwitch.checked = true;
        }

        // Toggle dark mode
        toggleSwitch.addEventListener('change', () => {
            html.classList.toggle('dark-mode');
            localStorage.setItem('darkMode', html.classList.contains('dark-mode'));
        });
    </script>
</body>
</html>

===== .:\Users\azizj\OneDrive\Documents\Aziz\GS\gs_project\gs_app\templates\orders\add_order.html =====
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Ajouter une Commande</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css">
    <style>
        /* Base Layout */
        html, body {
            height: 100%;
            margin: 0;
        }

        body {
            display: flex;
            flex-direction: column;
            min-height: 100vh;
            background-color: #f8f9fa;
            color: #212529;
            transition: all 0.3s ease;
        }

        .main-container {
            flex: 1 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            margin: 20px auto;
            max-width: 800px;
            transition: background-color 0.3s ease, box-shadow 0.3s ease;
        }

        footer {
            flex-shrink: 0;
            background-color: #f8f9fa;
            color: #212529;
            padding: 1rem 0;
            text-align: center;
        }

        /* Navbar */
        .navbar {
            background-color: #343a40;
            padding: 0.5rem 1rem;
        }

        .navbar-brand, .nav-link {
            color: white !important;
        }

        .nav-link:hover {
            color: #7ab8ff !important;
        }

        /* Dark Mode Styles */
        body.dark-mode {
            background-color: #121212;
            color: #e0e0e0;
        }

        .dark-mode .main-container {
            background-color: #1e1e1e;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
        }

        .dark-mode .navbar {
            background-color: #1e1e1e !important;
        }

        .dark-mode footer {
            background-color: #1e1e1e;
            color: #e0e0e0;
        }

        .dark-mode h2,
        .dark-mode .form-label,
        .dark-mode .form-text,
        .dark-mode label,
        .dark-mode p {
            color: #e0e0e0 !important;
        }

        .dark-mode .form-control,
        .dark-mode .form-select,
        .dark-mode input,
        .dark-mode textarea,
        .dark-mode select {
            background-color: #2d2d2d !important;
            color: #e0e0e0 !important;
            border-color: #444 !important;
        }

        .dark-mode .form-control:focus,
        .dark-mode .form-select:focus {
            background-color: #3d3d3d !important;
            border-color: #007bff !important;
            color: #e0e0e0 !important;
        }

        .dark-mode .btn-primary {
            background-color: #007bff;
            border-color: #007bff;
        }

        .dark-mode .btn-primary:hover {
            background-color: #0056b3;
            border-color: #004085;
        }

        .dark-mode a {
            color: #7ab8ff;
        }

        /* Dark Mode Toggle */
        .dark-mode-toggle-container {
            display: flex;
            align-items: center;
            margin-left: 15px;
        }

        .dark-mode-toggle {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 24px;
        }

        .dark-mode-toggle input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: 0.4s;
            border-radius: 24px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 16px;
            width: 16px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: 0.4s;
            border-radius: 50%;
        }

        .dark-mode-toggle input:checked + .slider {
            background-color: #007bff;
        }

        .dark-mode-toggle input:checked + .slider:before {
            transform: translateX(26px);
        }

        .dark-mode-toggle-label {
            margin-left: 10px;
            color: white;
        }

        .dark-mode .dark-mode-toggle-label {
            color: #e0e0e0;
        }

        /* Form Styling */
        h2 {
            color: #343a40;
            margin-bottom: 20px;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .btn-primary {
            transition: background-color 0.3s ease, border-color 0.3s ease;
        }
    </style>
</head>
<body>
    <!-- Navigation Bar -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="{% url 'home' %}">Home</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'order_list' %}">Commandes</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'product_list' %}">Produits</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'supplier_list' %}">Fournisseurs</a>
                    </li>
                </ul>
                <div class="dark-mode-toggle-container">
                    <label class="dark-mode-toggle">
                        <input type="checkbox" id="toggleSwitch">
                        <span class="slider"></span>
                    </label>
                    <span class="dark-mode-toggle-label">Mode Sombre</span>
                </div>
            </div>
        </div>
    </nav>

    <!-- Order Form -->
    <div class="container main-container">
        <h2>Ajouter une Commande</h2>
        <form method="POST" id="orderForm">
            {% csrf_token %}
            <div class="mb-3">
                <label for="id_status" class="form-label">Statut</label>
                {{ form.status }}
            </div>
            
            <input type="hidden" name="quantities" id="quantities" value="">
            
            <h3 class="mt-4">Sélectionner les produits</h3>
            <div class="row row-cols-1 row-cols-md-2 g-4 mb-4">
                {% for product in products %}
                <div class="col">
                    <div class="card product-card h-100">
                        <div class="card-body d-flex justify-content-between align-items-center">
                            <div>
                                <div class="form-check">
                                    <input class="form-check-input product-checkbox" type="checkbox" 
                                           name="products" value="{{ product.id }}" id="product{{ product.id }}">
                                    <label class="form-check-label" for="product{{ product.id }}">
                                        <strong>{{ product.name }}</strong>
                                        <span class="text-muted"> - {{ product.category }}</span>
                                    </label>
                                </div>
                                <div>Prix: {{ product.price }}€ | Stock: {{ product.quantity_in_stock }}</div>
                            </div>
                            <div class="quantity-control" style="display: none;">
                                <button type="button" class="btn btn-sm btn-outline-secondary decrease-qty">-</button>
                                <span class="quantity-value mx-2">1</span>
                                <button type="button" class="btn btn-sm btn-outline-secondary increase-qty">+</button>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
            
            <button type="submit" class="btn btn-primary">Ajouter la commande</button>
        </form>
    </div>

    <!-- Footer -->
    <footer>
        <p>© 2025 Gestion des Commandes - Tous droits réservés.</p>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Dark Mode Toggle
        const html = document.documentElement;
        const toggleSwitch = document.getElementById('toggleSwitch');

        // Load dark mode preference
        if (localStorage.getItem('darkMode') === 'true') {
            html.classList.add('dark-mode');
            toggleSwitch.checked = true;
        }

        // Toggle dark mode
        toggleSwitch.addEventListener('change', () => {
            html.classList.toggle('dark-mode');
            localStorage.setItem('darkMode', html.classList.contains('dark-mode'));
        });
        
        // Product selection and quantity handling
        document.addEventListener('DOMContentLoaded', function() {
            const checkboxes = document.querySelectorAll('.product-checkbox');
            const quantitiesInput = document.getElementById('quantities');
            const orderForm = document.getElementById('orderForm');
            
            // Show/hide quantity controls when product is selected
            checkboxes.forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    const card = this.closest('.card');
                    const quantityControl = card.querySelector('.quantity-control');
                    
                    if (this.checked) {
                        quantityControl.style.display = 'flex';
                    } else {
                        quantityControl.style.display = 'none';
                    }
                    
                    updateQuantitiesField();
                });
            });
            
            // Increase/decrease quantity buttons
            document.querySelectorAll('.increase-qty').forEach(btn => {
                btn.addEventListener('click', function() {
                    const valueSpan = this.parentElement.querySelector('.quantity-value');
                    let value = parseInt(valueSpan.textContent);
                    valueSpan.textContent = value + 1;
                    updateQuantitiesField();
                });
            });
            
            document.querySelectorAll('.decrease-qty').forEach(btn => {
                btn.addEventListener('click', function() {
                    const valueSpan = this.parentElement.querySelector('.quantity-value');
                    let value = parseInt(valueSpan.textContent);
                    if (value > 1) {
                        valueSpan.textContent = value - 1;
                        updateQuantitiesField();
                    }
                });
            });
            
            // Update hidden quantities field before form submission
            function updateQuantitiesField() {
                const quantities = [];
                
                checkboxes.forEach(checkbox => {
                    if (checkbox.checked) {
                        const productId = checkbox.value;
                        const card = checkbox.closest('.card');
                        const quantityValue = card.querySelector('.quantity-value').textContent;
                        quantities.push(`${productId}:${quantityValue}`);
                    }
                });
                
                quantitiesInput.value = quantities.join(',');
            }
            
            // Ensure quantities are updated on form submission
            orderForm.addEventListener('submit', function(e) {
                updateQuantitiesField();
            });
        });
    </script>
</body>
</html>


===== .:\Users\azizj\OneDrive\Documents\Aziz\GS\gs_project\gs_app\templates\orders\orderform =====


===== .:\Users\azizj\OneDrive\Documents\Aziz\GS\gs_project\gs_app\templates\orders\order_confirm_delete.html =====
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Confirmation de Suppression</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>Confirmation de Suppression</h1>
        <p>Êtes-vous sûr de vouloir supprimer la commande {{ order.id }} ?</p>
        <form method="POST">
            {% csrf_token %}
            <button type="submit" class="btn btn-danger">Oui, supprimer</button>
            <a href="{% url 'order_list' %}" class="btn btn-secondary">Annuler</a>
        </form>
    </div>
</body>
</html>

===== .:\Users\azizj\OneDrive\Documents\Aziz\GS\gs_project\gs_app\templates\orders\order_detail_confirm_delete.html =====
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Confirmation de Suppression du Détail</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>Confirmation de Suppression du Détail</h1>
        <p>Êtes-vous sûr de vouloir supprimer le détail du produit {{ order_detail.product.name }} ?</p>
        <form method="POST">
            {% csrf_token %}
            <button type="submit" class="btn btn-danger">Oui, supprimer</button>
            <a href="{% url 'order_detail_list' order_detail.order.id %}" class="btn btn-secondary">Annuler</a>
        </form>
    </div>
</body>
</html>

===== .:\Users\azizj\OneDrive\Documents\Aziz\GS\gs_project\gs_app\templates\orders\order_detail_form.html =====
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Ajouter un Détail de Commande</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css">
</head>
<body>
    <!-- Barre de navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="{% url 'home' %}">Gestion Commandes</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item"><a class="nav-link" href="{% url 'order_list' %}">Commandes</a></li>
                    <li class="nav-item"><a class="nav-link" href="{% url 'product_list' %}">Produits</a></li>
                    <li class="nav-item"><a class="nav-link" href="{% url 'supplier_list' %}">Fournisseurs</a></li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Formulaire Détail de Commande -->
    <div class="container mt-4">
        <h2>Ajouter un Détail de Commande</h2>
        <form method="POST">
            {% csrf_token %}
            {{ form.as_p }}
            <button type="submit" class="btn btn-primary">Ajouter le détail</button>
        </form>
    </div>

    <footer class="text-center mt-4 py-3 bg-light">
        <p>&copy; 2025 Gestion des Commandes - Tous droits réservés.</p>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

===== .:\Users\azizj\OneDrive\Documents\Aziz\GS\gs_project\gs_app\templates\orders\order_detail_list.html =====
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Détails de la commande</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css">
    <style>
        /* Base Layout */
        html, body {
            height: 100%;
            margin: 0;
        }

        body {
            display: flex;
            flex-direction: column;
            min-height: 100vh;
            background-color: #f8f9fa;
            color: #212529;
            transition: all 0.3s ease;
        }

        .main-container {
            flex: 1 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            margin: 20px auto;
            max-width: 800px;
            transition: background-color 0.3s ease, box-shadow 0.3s ease;
        }

        footer {
            flex-shrink: 0;
            background-color: #f8f9fa;
            color: #212529;
            padding: 1rem 0;
            text-align: center;
        }

        /* Navbar */
        .navbar {
            background-color: #343a40;
            padding: 0.5rem 1rem;
        }

        .navbar-brand, .nav-link {
            color: white !important;
        }

        .nav-link:hover {
            color: #7ab8ff !important;
        }

        /* Dark Mode Styles */
        body.dark-mode {
            background-color: #121212;
            color: #e0e0e0;
        }

        .dark-mode .main-container {
            background-color: #1e1e1e;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
        }

        .dark-mode .navbar {
            background-color: #1e1e1e !important;
        }

        .dark-mode footer {
            background-color: #1e1e1e;
            color: #e0e0e0;
        }

        .dark-mode h2,
        .dark-mode .form-label,
        .dark-mode .form-text,
        .dark-mode label,
        .dark-mode p {
            color: #e0e0e0 !important;
        }

        .dark-mode .form-control,
        .dark-mode .form-select,
        .dark-mode input,
        .dark-mode textarea,
        .dark-mode select {
            background-color: #2d2d2d !important;
            color: #e0e0e0 !important;
            border-color: #444 !important;
        }

        .dark-mode .form-control:focus,
        .dark-mode .form-select:focus {
            background-color: #3d3d3d !important;
            border-color: #007bff !important;
            color: #e0e0e0 !important;
        }

        .dark-mode .btn-primary {
            background-color: #007bff;
            border-color: #007bff;
        }

        .dark-mode .btn-primary:hover {
            background-color: #0056b3;
            border-color: #004085;
        }

        .dark-mode .btn-secondary {
            background-color: #6c757d;
            border-color: #6c757d;
        }

        .dark-mode .btn-secondary:hover {
            background-color: #5a6268;
            border-color: #545b62;
        }

        .dark-mode a {
            color: #7ab8ff;
        }

        .dark-mode .table {
            background-color: #1e1e1e;
            color: #e0e0e0;
        }

        .dark-mode .table-dark {
            background-color: #333;
            color: #e0e0e0;
        }

        .dark-mode .table-striped tbody tr:nth-child(odd) {
            background-color: #2c2c2c;
        }

        .dark-mode .table-bordered td,
        .dark-mode .table-bordered th {
            border-color: #444;
        }

        /* Dark Mode Toggle */
        .dark-mode-toggle-container {
            display: flex;
            align-items: center;
            margin-left: 15px;
        }

        .dark-mode-toggle {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 24px;
        }

        .dark-mode-toggle input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: 0.4s;
            border-radius: 24px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 16px;
            width: 16px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: 0.4s;
            border-radius: 50%;
        }

        .dark-mode-toggle input:checked + .slider {
            background-color: #007bff;
        }

        .dark-mode-toggle input:checked + .slider:before {
            transform: translateX(26px);
        }

        .dark-mode-toggle-label {
            margin-left: 10px;
            color: white;
        }

        .dark-mode .dark-mode-toggle-label {
            color: #e0e0e0;
        }

        /* General Styling */
        h2 {
            color: #343a40;
            margin-bottom: 20px;
        }

        .table {
            transition: background-color 0.3s ease;
        }

        .btn {
            transition: background-color 0.3s ease, border-color 0.3s ease;
        }
    </style>
</head>
<body>
    <!-- Navigation Bar -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="{% url 'home' %}">Home</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'order_list' %}">Commandes</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'product_list' %}">Produits</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'supplier_list' %}">Fournisseurs</a>
                    </li>
                </ul>
                <div class="dark-mode-toggle-container">
                    <label class="dark-mode-toggle">
                        <input type="checkbox" id="toggleSwitch">
                        <span class="slider"></span>
                    </label>
                    <span class="dark-mode-toggle-label">Mode Sombre</span>
                </div>
            </div>
        </div>
    </nav>

    <!-- Order Details -->
    <div class="container main-container">
        <h2>Détails de la commande #{{ order.id }}</h2>
        <div class="table-responsive">
            <table class="table table-striped table-bordered">
                <thead class="table-dark">
                    <tr>
                        <th>Produit</th>
                        <th>Quantité</th>
                        <th>Prix à la commande</th>
                    </tr>
                </thead>
                <tbody>
                    {% for detail in details %}
                        <tr>
                            <td>{{ detail.product.name }}</td>
                            <td>{{ detail.quantity }}</td>
                            <td>{{ detail.price_at_order }}</td>
                        </tr>
                    {% empty %}
                        <tr>
                            <td colspan="3" class="text-center">Aucun détail disponible pour cette commande.</td>
                        </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        <div class="mt-4">
            <a href="{% url 'add_order_detail' order.id %}" class="btn btn-primary">Ajouter un détail</a>
            <a href="{% url 'order_list' %}" class="btn btn-secondary ms-2">Retour à la liste des commandes</a>
        </div>
    </div>

    <!-- Footer -->
    <footer>
        <p>© 2025 Gestion des Commandes - Tous droits réservés.</p>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Dark Mode Toggle
        const html = document.documentElement;
        const toggleSwitch = document.getElementById('toggleSwitch');

        // Load dark mode preference
        if (localStorage.getItem('darkMode') === 'true') {
            html.classList.add('dark-mode');
            toggleSwitch.checked = true;
        }

        // Toggle dark mode
        toggleSwitch.addEventListener('change', () => {
            html.classList.toggle('dark-mode');
            localStorage.setItem('darkMode', html.classList.contains('dark-mode'));
        });
    </script>
</body>
</html>

===== .:\Users\azizj\OneDrive\Documents\Aziz\GS\gs_project\gs_app\templates\orders\order_edit_form_fields.html =====
<div class="mb-3">
    <label for="id_status" class="form-label">Statut</label>
    {{ form.status }}
    {% if order.status == 'pending' %}
    <div class="form-text text-info mt-1">
        <i class="bi bi-info-circle"></i> Changer le statut à "Terminée" mettra à jour les quantités de produits en stock.
    </div>
    {% endif %}
</div>

{% if order_details %}
<h5 class="mt-4 mb-3">Détails de la commande</h5>
<div class="table-responsive">
    <table class="table table-sm">
        <thead>
            <tr>
                <th>Produit</th>
                <th>Quantité</th>
                <th>Prix unitaire</th>
                <th>Total</th>
            </tr>
        </thead>
        <tbody>
            {% for detail in order_details %}
            <tr>
                <td>{{ detail.product.name }}</td>
                <td>{{ detail.quantity }}</td>
                <td>{{ detail.price_at_order }}€</td>
                <td>{{ detail.get_total }}€</td>
            </tr>
            {% endfor %}
        </tbody>
        <tfoot>
            <tr>
                <th colspan="3" class="text-end">Total de la commande:</th>
                <th>{{ order.get_total_price }}€</th>
            </tr>
        </tfoot>
    </table>
</div>
<div class="mt-3">
    <a href="{% url 'order_detail_list' order.id %}" class="btn btn-outline-info btn-sm" target="_blank">
        Gérer les détails de la commande
    </a>
</div>
{% else %}
<div class="alert alert-info mt-3">
    Cette commande ne contient aucun produit. 
    <a href="{% url 'order_detail_list' order.id %}" target="_blank">Ajouter des produits</a>
</div>
{% endif %}


===== .:\Users\azizj\OneDrive\Documents\Aziz\GS\gs_project\gs_app\templates\orders\order_form_fields.html =====
<div class="mb-3">
    <label for="id_status" class="form-label">Statut</label>
    {{ form.status }}
</div>

<!-- Hidden field for quantities -->
<input type="hidden" name="quantities" id="quantities" value="">

<h5 class="mt-4 mb-3">Sélectionner les produits</h5>
<div class="row row-cols-1 row-cols-md-2 g-4">
    {% for product in products %}
    <div class="col">
        <div class="card product-card h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-start mb-2">
                    <div class="form-check">
                        <input class="form-check-input product-checkbox" type="checkbox" 
                               name="products" value="{{ product.id }}" id="product{{ product.id }}">
                        <label class="form-check-label" for="product{{ product.id }}">
                            <strong>{{ product.name }}</strong>
                        </label>
                    </div>
                </div>
                <div class="product-info">
                    <div><small class="text-muted">Catégorie: {{ product.category }}</small></div>
                    <div><small class="text-muted">Prix: {{ product.price }}€</small></div>
                    <div><small class="text-muted">Stock: {{ product.quantity_in_stock }}</small></div>
                </div>
                <div class="quantity-control mt-2" style="display: none;">
                    <label class="form-label">Quantité:</label>
                    <div class="input-group input-group-sm">
                        <button type="button" class="btn btn-outline-secondary decrease-qty">-</button>
                        <input type="number" class="form-control text-center quantity-input" 
                               value="1" min="1" max="{{ product.quantity_in_stock }}">
                        <button type="button" class="btn btn-outline-secondary increase-qty">+</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endfor %}
</div>

<script>
    // Initialize the form controls after the content is loaded
    (function() {
        const checkboxes = document.querySelectorAll('.product-checkbox');
        const quantitiesInput = document.getElementById('quantities');
        
        // Show/hide quantity controls when product is selected
        checkboxes.forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                const card = this.closest('.card');
                const quantityControl = card.querySelector('.quantity-control');
                
                if (this.checked) {
                    quantityControl.style.display = 'block';
                } else {
                    quantityControl.style.display = 'none';
                }
                
                updateQuantitiesField();
            });
        });
        
        // Increase quantity button
        document.querySelectorAll('.increase-qty').forEach(btn => {
            btn.addEventListener('click', function() {
                const input = this.parentElement.querySelector('.quantity-input');
                let value = parseInt(input.value);
                let max = parseInt(input.getAttribute('max'));
                
                if (value < max) {
                    input.value = value + 1;
                    updateQuantitiesField();
                }
            });
        });
        
        // Decrease quantity button
        document.querySelectorAll('.decrease-qty').forEach(btn => {
            btn.addEventListener('click', function() {
                const input = this.parentElement.querySelector('.quantity-input');
                let value = parseInt(input.value);
                
                if (value > 1) {
                    input.value = value - 1;
                    updateQuantitiesField();
                }
            });
        });
        
        // Handle direct input changes
        document.querySelectorAll('.quantity-input').forEach(input => {
            input.addEventListener('change', function() {
                validateQuantityInput(this);
                updateQuantitiesField();
            });
            
            input.addEventListener('input', function() {
                validateQuantityInput(this);
                updateQuantitiesField();
            });
        });
        
        // Validate quantity input
        function validateQuantityInput(input) {
            let value = parseInt(input.value);
            let max = parseInt(input.getAttribute('max'));
            
            if (isNaN(value) || value < 1) {
                input.value = 1;
            } else if (value > max) {
                input.value = max;
            }
        }
        
        // Update hidden quantities field
        function updateQuantitiesField() {
            const quantities = [];
            
            checkboxes.forEach(checkbox => {
                if (checkbox.checked) {
                    const productId = checkbox.value;
                    const card = checkbox.closest('.card');
                    const quantityInput = card.querySelector('.quantity-input');
                    quantities.push(`${productId}:${quantityInput.value}`);
                }
            });
            
            quantitiesInput.value = quantities.join(',');
            console.log("Updated quantities:", quantitiesInput.value); // Debug log
        }
        
        // Initialize quantities field
        updateQuantitiesField();
    })(); // Immediately invoke the function
</script>





===== .:\Users\azizj\OneDrive\Documents\Aziz\GS\gs_project\gs_app\templates\orders\order_list.html =====
{% load static %}
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Liste des Commandes</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css">
    <link href="{% static 'css/orders.css' %}" rel="stylesheet">
</head>
<body>
    <!-- Navigation Bar -->
    <nav class="navbar navbar-expand-lg shadow-sm">
        <div class="container">
            <a class="navbar-brand d-flex align-items-center" href="{% url 'home' %}">
                <span class="fw-bold text-primary">SNDP</span>
                <span class="ms-1">Agil</span>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav mx-auto">
                    <li class="nav-item mx-2">
                        <a class="nav-link" href="{% url 'product_list' %}">
                            <i class="bi bi-fuel-pump"></i> Produits
                        </a>
                    </li>
                    <li class="nav-item mx-2">
                        <a class="nav-link active" href="{% url 'order_list' %}">
                            <i class="bi bi-cart3"></i> Commandes
                        </a>
                    </li>
                    <li class="nav-item mx-2">
                        <a class="nav-link" href="{% url 'supplier_list' %}">
                            <i class="bi bi-building"></i> Fournisseurs
                        </a>
                    </li>
                </ul>
                <div class="dark-mode-toggle-container">
                    <label class="dark-mode-toggle">
                        <input type="checkbox" id="darkModeToggle">
                        <span class="slider"></span>
                    </label>
                    <span class="dark-mode-toggle-label">Mode Sombre</span>
                </div>
            </div>
        </div>
    </nav>

    <!-- Header Section -->
    <div class="header-section">
        <div class="container">
            <div class="header-content text-center">
                <h1>
                    <i class="bi bi-cart3 me-2"></i>
                    Gestion des Commandes
                </h1>
                <p>Gérez vos commandes de produits pétroliers</p>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="container main-container">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="order-list">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2 class="mb-0">Liste des Commandes</h2>
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addOrderModal">
                            <i class="bi bi-plus-circle"></i> Ajouter une Commande
                        </button>
                    </div>

                    {% if orders %}
                        {% for order in orders %}
                            <div class="order-item">
                                <div class="order-info">
                                    <strong>Commande #{{ order.id }}</strong>
                                    <span class="text-muted"> - {{ order.status }}</span>
                                    <div>Date: {{ order.date|date:"d/m/Y" }}</div>
                                </div>
                                <div class="order-actions">
                                    <button class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#editOrderModal{{ order.id }}">
                                        <i class="bi bi-pencil"></i> Modifier
                                    </button>
                                    <button class="btn btn-sm btn-outline-danger" data-bs-toggle="modal" data-bs-target="#deleteOrderModal{{ order.id }}">
                                        <i class="bi bi-trash"></i> Supprimer
                                    </button>
                                    <a href="{% url 'order_detail_list' order.id %}" class="btn btn-sm btn-outline-info">
                                        <i class="bi bi-list-ul"></i> Détails
                                    </a>
                                </div>
                            </div>

                            <!-- Edit Order Modal -->
                            <div class="modal fade" id="editOrderModal{{ order.id }}" tabindex="-1" aria-hidden="true">
                                <div class="modal-dialog">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h5 class="modal-title">Modifier la Commande #{{ order.id }}</h5>
                                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                        </div>
                                        <div class="modal-body">
                                            <form method="post" action="{% url 'edit_order' order.id %}" id="editOrderForm{{ order.id }}">
                                                {% csrf_token %}
                                                <div id="editFormContent{{ order.id }}">Chargement...</div>
                                            </form>
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                                            <button type="submit" form="editOrderForm{{ order.id }}" class="btn btn-primary">Enregistrer</button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Delete Order Modal -->
                            <div class="modal fade" id="deleteOrderModal{{ order.id }}" tabindex="-1" aria-hidden="true">
                                <div class="modal-dialog">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h5 class="modal-title">Confirmer la suppression</h5>
                                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                        </div>
                                        <div class="modal-body">
                                            <p>Êtes-vous sûr de vouloir supprimer la commande #{{ order.id }}?</p>
                                            <p class="text-danger">Cette action est irréversible.</p>
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                                            <form action="{% url 'delete_order' order.id %}" method="post">
                                                {% csrf_token %}
                                                <button type="submit" class="btn btn-danger">Supprimer</button>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                    {% else %}
                        <div class="alert alert-info">Aucune commande trouvée.</div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Add Order Modal -->
    <div class="modal fade" id="addOrderModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Ajouter une Commande</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form method="post" action="{% url 'add_order' %}" id="addOrderForm">
                        {% csrf_token %}
                        <div id="addOrderFormContent">Chargement...</div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="submit" form="addOrderForm" class="btn btn-primary">Ajouter</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer>
        <p>© 2025 Gestion des Commandes - Tous droits réservés.</p>
    </footer>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Dark Mode Toggle
        const html = document.documentElement;
        const toggleSwitch = document.getElementById('darkModeToggle');

        // Load dark mode preference
        if (localStorage.getItem('darkMode') === 'true') {
            html.classList.add('dark-mode');
            toggleSwitch.checked = true;
        }

        // Toggle dark mode
        toggleSwitch.addEventListener('change', () => {
            html.classList.toggle('dark-mode');
            localStorage.setItem('darkMode', html.classList.contains('dark-mode'));
        });

        // AJAX for loading form content
        document.addEventListener('DOMContentLoaded', function() {
            // Load add form content when modal is shown
            const addModal = document.getElementById('addOrderModal');
            if (addModal) {
                addModal.addEventListener('show.bs.modal', function() {
                    fetch('{% url "add_order" %}?format=form')
                        .then(response => {
                            if (!response.ok) throw new Error('Network response was not ok');
                            return response.text();
                        })
                        .then(html => {
                            const addFormContent = document.getElementById('addOrderFormContent');
                            if (addFormContent) {
                                addFormContent.innerHTML = html;
                                // Execute any scripts in the loaded content
                                const scripts = addFormContent.querySelectorAll('script');
                                scripts.forEach(script => {
                                    const newScript = document.createElement('script');
                                    newScript.textContent = script.textContent;
                                    addFormContent.appendChild(newScript);
                                    script.remove(); // Remove the original script
                                });
                            }
                        })
                        .catch(error => {
                            console.error('Error loading add form:', error);
                            const addFormContent = document.getElementById('addOrderFormContent');
                            if (addFormContent) {
                                addFormContent.innerHTML =
                                    '<div class="alert alert-danger">Erreur lors du chargement du formulaire. Veuillez vérifier votre connexion ou réessayer plus tard.</div>';
                            }
                        });
                });
            }

            // Load edit form content when modals are shown
            {% if orders %}
            const orderData = [
                {% for order in orders %}
                {
                    "id": "{{ order.id|escapejs }}",
                    "editUrl": "{% url 'edit_order' order.id %}?format=form"
                }{% if not forloop.last %},{% endif %}
                {% endfor %}
            ];

            // Process each order if orderData exists and is not empty
            if (orderData && orderData.length > 0) {
                orderData.forEach(function(order) {
                    const modalId = 'editOrderModal' + order.id;
                    const contentId = 'editFormContent' + order.id;
                    const formId = 'editOrderForm' + order.id;
                    const editModal = document.getElementById(modalId);

                    if (editModal) {
                        // Load form content when modal is shown
                        editModal.addEventListener('show.bs.modal', function() {
                            fetch(order.editUrl)
                                .then(response => {
                                    if (!response.ok) throw new Error('Network response was not ok');
                                    return response.text();
                                })
                                .then(html => {
                                    const contentElement = document.getElementById(contentId);
                                    if (contentElement) {
                                        contentElement.innerHTML = html;
                                        // Execute any scripts in the loaded content
                                        const scripts = contentElement.querySelectorAll('script');
                                        scripts.forEach(script => {
                                            const newScript = document.createElement('script');
                                            newScript.textContent = script.textContent;
                                            contentElement.appendChild(newScript);
                                            script.remove(); // Remove the original script
                                        });
                                    }
                                })
                                .catch(error => {
                                    console.error('Error loading edit form for order ' + order.id + ':', error);
                                    const contentElement = document.getElementById(contentId);
                                    if (contentElement) {
                                        contentElement.innerHTML =
                                            '<div class="alert alert-danger">Erreur lors du chargement du formulaire. Veuillez vérifier votre connexion ou réessayer plus tard.</div>';
                                    }
                                });
                        });

                        // Handle form submission
                        const form = document.getElementById(formId);
                        if (form) {
                            form.addEventListener('submit', function(e) {
                                e.preventDefault(); // Prevent default form submission

                                // Submit form via fetch API
                                fetch(form.action, {
                                    method: 'POST',
                                    body: new FormData(form),
                                    headers: {
                                        'X-Requested-With': 'XMLHttpRequest'
                                    }
                                })
                                .then(response => {
                                    if (!response.ok) throw new Error('Network response was not ok');
                                    // Close the modal and reload the page to show updated data
                                    const modal = bootstrap.Modal.getInstance(editModal);
                                    modal.hide();
                                    window.location.reload();
                                })
                                .catch(error => {
                                    console.error('Error submitting form:', error);
                                    alert('Erreur lors de la soumission du formulaire. Veuillez réessayer.');
                                });
                            });
                        }
                    }
                });
            }
            {% else %}
            // Handle case where no orders exist
            console.log('No orders available to process.');
            {% endif %}
        });
    </script>
</body>
</html>
```

===== .:\Users\azizj\OneDrive\Documents\Aziz\GS\gs_project\gs_app\templates\products\product_form.html =====
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modifier le Produit</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { padding: 20px; background-color: #f8f9fa; }
        .form-container { 
            max-width: 600px; 
            margin: 0 auto; 
            background: white; 
            padding: 25px; 
            border-radius: 8px; 
            box-shadow: 0 0 15px rgba(0,0,0,0.1);
        }
        .btn-container { margin-top: 20px; }

        /* Dark Mode Styles */
        .dark-mode body {
            background-color: #121212;
            color: #e0e0e0;
        }
        .dark-mode .form-container {
            background-color: #1e1e1e;
            box-shadow: 0 0 15px rgba(255, 255, 255, 0.1);
        }
        .dark-mode .btn-outline-secondary, .dark-mode .btn-primary {
            color: #90caf9;
            border-color: #444;
        }
        .dark-mode .btn-outline-secondary:hover, .dark-mode .btn-primary:hover {
            color: #ffffff;
            border-color: #666;
        }
    </style>
</head>
<body>

    <!-- Navbar with Dark Mode Toggle -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container mt-4">
            <a class="navbar-brand" href="{% url 'home' %}">Home</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item"><a class="nav-link" href="{% url 'order_list' %}">Commandes</a></li>
                    <li class="nav-item"><a class="nav-link" href="{% url 'product_list' %}">Produits</a></li>
                    <li class="nav-item"><a class="nav-link" href="{% url 'supplier_list' %}">Fournisseurs</a></li>
                </ul>
                <!-- Dark Mode Toggle -->
                <div class="form-check form-switch text-light">
                    <input class="form-check-input" type="checkbox" id="darkModeToggle">
                    <label class="form-check-label" for="darkModeToggle">Mode sombre</label>
                </div>
            </div>
        </div>
    </nav>

    <!-- Product Edit Form -->
    <div class="form-container">
        <h2 class="mb-4">Modifier {{ product.name }}</h2>
        
        <form method="post" id="productForm">
            {% csrf_token %}
            
            <div class="mb-3">
                {{ form.as_p }}
            </div>
            
            <!-- Hidden field for restock choice -->
            <input type="hidden" name="restock" id="restockField" value="no">
            
            <div class="btn-container d-flex justify-content-between">
                <a href="{% url 'product_list' %}" class="btn btn-outline-secondary">Retour</a>
                <button type="submit" class="btn btn-primary">Enregistrer</button>
            </div>
        </form>
    </div>

    <script>
        document.getElementById('productForm').addEventListener('submit', function(e) {
            const quantityInput = document.querySelector('input[name="quantity_in_stock"]');
            const currentQuantity = parseInt(quantityInput.value);
            const restockField = document.getElementById('restockField');
            
            if (currentQuantity < 100) {
                const confirmRestock = confirm('Quantité faible (' + currentQuantity + '). Voulez-vous réapprovisionner (+100)?');
                
                if (confirmRestock) {
                    restockField.value = 'yes';
                    quantityInput.value = currentQuantity + 100; // Update for user feedback
                } else {
                    restockField.value = 'no';
                }
            }
        });

        // Dark Mode Toggle Logic
        const toggle = document.getElementById('darkModeToggle');
        const html = document.documentElement;

        // Load preference
        if (localStorage.getItem('darkMode') === 'true') {
            html.classList.add('dark-mode');
            toggle.checked = true;
        }

        toggle.addEventListener('change', function () {
            html.classList.toggle('dark-mode');
            localStorage.setItem('darkMode', html.classList.contains('dark-mode'));
        });
    </script>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>


===== .:\Users\azizj\OneDrive\Documents\Aziz\GS\gs_project\gs_app\templates\products\product_form_fields.html =====
{{ form.as_p }}

{% if product and product.quantity_in_stock < 100 %}
<div class="alert alert-warning">
    <strong>Attention!</strong> Stock faible ({{ product.quantity_in_stock }} unités).
</div>
{% endif %}

<script>
    // This script will run when the form is loaded via AJAX
    (function() {
        const quantityInput = document.querySelector('input[name="quantity_in_stock"]');
        const restockField = document.getElementById('restockField{{ product.id }}');
        
        if (quantityInput && restockField) {
            // Find the form containing this input
            const form = quantityInput.closest('form');
            
            // Remove any existing submit event listeners to avoid duplicates
            const clonedForm = form.cloneNode(true);
            form.parentNode.replaceChild(clonedForm, form);
            
            // Add the event listener to the form
            clonedForm.addEventListener('submit', function(e) {
                const currentQuantity = parseInt(quantityInput.value);
                
                if (currentQuantity < 100) {
                    const confirmRestock = confirm('Quantité faible (' + currentQuantity + '). Voulez-vous réapprovisionner (+100)?');
                    
                    if (confirmRestock) {
                        restockField.value = 'yes';
                    } else {
                        restockField.value = 'no';
                    }
                }
            });
        }
    })();
</script>


===== .:\Users\azizj\OneDrive\Documents\Aziz\GS\gs_project\gs_app\templates\products\product_list.html =====
{% load static %}
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Liste des Produits</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css">
    <link href="{% static 'css/products.css' %}" rel="stylesheet">
</head>
<body>
    <!-- Navigation Bar -->
    <nav class="navbar navbar-expand-lg shadow-sm">
        <div class="container">
            <a class="navbar-brand d-flex align-items-center" href="{% url 'home' %}">
                <span class="fw-bold text-primary">SNDP</span>
                <span class="ms-1">Agil</span>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav mx-auto">
                    <li class="nav-item mx-2">
                        <a class="nav-link {% if request.resolver_match.url_name == 'product_list' %}active{% endif %}" href="{% url 'product_list' %}">
                            <i class="bi bi-box me-1"></i>Produits
                        </a>
                    </li>
                    <li class="nav-item mx-2">
                        <a class="nav-link {% if request.resolver_match.url_name == 'order_list' %}active{% endif %}" href="{% url 'order_list' %}">
                            <i class="bi bi-cart me-1"></i>Commandes
                        </a>
                    </li>
                    <li class="nav-item mx-2">
                        <a class="nav-link {% if request.resolver_match.url_name == 'supplier_list' %}active{% endif %}" href="{% url 'supplier_list' %}">
                            <i class="bi bi-building me-1"></i>Fournisseurs
                        </a>
                    </li>
                </ul>
                <div class="dark-mode-toggle-container d-flex align-items-center">
                    <label class="dark-mode-toggle me-2">
                        <input type="checkbox" id="darkModeToggle">
                        <span class="slider"></span>
                    </label>
                    <span class="text-nowrap fs-sm">Mode Sombre</span>
                </div>
            </div>
        </div>
    </nav>

    <!-- Header Section -->
    <div class="header-section">
        <div class="container">
            <div class="header-content text-center">
                <h1>
                    <i class="bi bi-box-seam me-2"></i>
                    Gestion des Produits
                </h1>
                <p>Gérez votre inventaire de produits pétroliers</p>
            </div>
        </div>
    </div>

    <div class="container main-container">
        <div class="product-list">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h2>Liste des Produits</h2>
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addProductModal">
                    <i class="bi bi-plus-circle"></i> Ajouter un Produit
                </button>
            </div>

            <div class="list-group">
                {% for product in products %}
                <div class="product-item">
                    <div class="product-info">
                        <strong>{{ product.name }}</strong>
                        <span class="text-muted"> - {{ product.category }}</span>
                        <div>Prix: {{ product.price }}€ | Stock: {{ product.quantity_in_stock }}</div>
                    </div>
                    <div class="product-actions">
                        <button class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#editProductModal{{ product.id }}">
                            <i class="bi bi-pencil"></i> Modifier
                        </button>
                        <button class="btn btn-sm btn-outline-danger" data-bs-toggle="modal" data-bs-target="#deleteProductModal{{ product.id }}">
                            <i class="bi bi-trash"></i> Supprimer
                        </button>
                    </div>
                </div>

                <!-- Edit Product Modal -->
                <div class="modal fade" id="editProductModal{{ product.id }}" tabindex="-1" aria-hidden="true">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">Modifier {{ product.name }}</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                <form method="post" action="{% url 'edit_product' product.id %}" id="editProductForm{{ product.id }}">
                                    {% csrf_token %}
                                    <div id="editFormContent{{ product.id }}">Chargement...</div>
                                    <input type="hidden" name="restock" id="restockField{{ product.id }}" value="no">
                                </form>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                                <button type="submit" form="editProductForm{{ product.id }}" class="btn btn-primary">Enregistrer</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Delete Product Modal -->
                <div class="modal fade" id="deleteProductModal{{ product.id }}" tabindex="-1" aria-hidden="true">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">Confirmer la suppression</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                <p>Êtes-vous sûr de vouloir supprimer <strong>{{ product.name }}</strong>?</p>
                                <p class="text-danger">Cette action est irréversible.</p>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                                <form action="{% url 'delete_product' product.id %}" method="post">
                                    {% csrf_token %}
                                    <button type="submit" class="btn btn-danger">Supprimer</button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
                {% empty %}
                <div class="alert alert-info">Aucun produit trouvé.</div>
                {% endfor %}
            </div>
        </div>
    </div>

    <!-- Add Product Modal -->
    <div class="modal fade" id="addProductModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Ajouter un Produit</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form method="post" action="{% url 'add_product' %}" id="addProductForm">
                        {% csrf_token %}
                        <div id="addFormContent">Chargement...</div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="submit" form="addProductForm" class="btn btn-primary">Ajouter</button>
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Dark Mode Toggle and AJAX Script -->
    <script>
        // Dark Mode Toggle
        const toggle = document.getElementById('darkModeToggle');
        const html = document.documentElement;

        // Load preference
        if (localStorage.getItem('darkMode') === 'true') {
            html.classList.add('dark-mode');
            toggle.checked = true;
        }

        toggle.addEventListener('change', function() {
            html.classList.toggle('dark-mode');
            localStorage.setItem('darkMode', html.classList.contains('dark-mode'));
        });

        // AJAX for loading form content
        document.addEventListener('DOMContentLoaded', function() {
            // Load add form content when modal is shown
            const addModal = document.getElementById('addProductModal');
            if (addModal) {
                addModal.addEventListener('show.bs.modal', function() {
                    fetch('{% url "add_product" %}?format=form')
                        .then(response => {
                            if (!response.ok) throw new Error('Network response was not ok');
                            return response.text();
                        })
                        .then(html => {
                            document.getElementById('addFormContent').innerHTML = html;
                        })
                        .catch(error => {
                            console.error('Error loading add form:', error);
                            document.getElementById('addFormContent').innerHTML =
                                '<div class="alert alert-danger">Erreur lors du chargement du formulaire. Veuillez vérifier votre connexion ou réessayer plus tard.</div>';
                        });
                });
            }

            // Load edit form content when modals are shown
            const productData = [
            {% for product in products %}
                {
                    "id": "{{ product.id }}",
                    "editUrl": "{% url 'edit_product' product.id %}?format=form"
                }{% if not forloop.last %},{% endif %}
            {% endfor %}
            ];

            // Process each product
            productData.forEach(function(product) {
                const modalId = 'editProductModal' + product.id;
                const contentId = 'editFormContent' + product.id;
                const restockFieldId = 'restockField' + product.id;
                const editModal = document.getElementById(modalId);

                if (editModal) {
                    editModal.addEventListener('show.bs.modal', function() {
                        fetch(product.editUrl)
                            .then(response => {
                                if (!response.ok) throw new Error('Network response was not ok');
                                return response.text();
                            })
                            .then(html => {
                                document.getElementById(contentId).innerHTML = html;

                                // Add submit event listener to the form after it's loaded
                                const form = document.getElementById('editProductForm' + product.id);
                                if (form) {
                                    form.addEventListener('submit', function(e) {
                                        const quantityInput = form.querySelector('input[name="quantity_in_stock"]');
                                        if (quantityInput) {
                                            const currentQuantity = parseInt(quantityInput.value);
                                            const restockField = document.getElementById(restockFieldId);

                                            if (currentQuantity < 100 && restockField) {
                                                e.preventDefault(); // Prevent form submission
                                                const confirmRestock = confirm('Quantité faible (' + currentQuantity + '). Voulez-vous réapprovisionner (+100)?');

                                                if (confirmRestock) {
                                                    restockField.value = 'yes';
                                                    quantityInput.value = currentQuantity + 100; // Update for user feedback
                                                } else {
                                                    restockField.value = 'no';
                                                }

                                                // Continue with form submission
                                                form.submit();
                                            }
                                        }
                                    });
                                }
                            })
                            .catch(error => {
                                console.error('Error loading edit form for product ' + product.id + ':', error);
                                document.getElementById(contentId).innerHTML =
                                    '<div class="alert alert-danger">Erreur lors du chargement du formulaire. Veuillez vérifier votre connexion ou réessayer plus tard.</div>';
                            });
                    });
                }
            });
        });
    </script>
</body>
</html>


===== .:\Users\azizj\OneDrive\Documents\Aziz\GS\gs_project\gs_app\templates\registration\login.html =====
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - SNDP</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            background-color: #f4f4f4;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            background-image: url('/static/background.png'); /* Correct path to static image */
            background-size: cover;
            background-position: center;
        }

        .login-container {
            background-color: rgba(255, 255, 255, 0.8); 
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            width: 400px;
        }

        .login-box {
            width: 100%;
            text-align: center;
        }

        h2 {
            color: #000;
            margin-bottom: 30px;
            font-size: 28px;
            font-weight: bold;
        }

        .textbox {
            margin-bottom: 25px;
        }

        .textbox input {
            width: 100%;
            padding: 15px;
            border: 2px solid #ccc;
            border-radius: 5px;
            font-size: 18px;
        }

        .textbox input:focus {
            border-color: #ffcc00;
            outline: none;
        }

        .btn {
            width: 100%;
            padding: 15px;
            background-color: #ffcc00;
            border: none;
            color: #000;
            font-size: 20px;
            cursor: pointer;
            border-radius: 5px;
        }

        .btn:hover {
            background-color: #ffb800;
        }

        .signup-link {
            margin-top: 20px;
            font-size: 16px;
        }

        .signup-link a {
            color: #ffcc00;
            text-decoration: none;
        }

        .signup-link a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-box">
            <h2>Login</h2>
            <!-- Form with CSRF Token -->
            <form action="{% url 'login' %}" method="POST">
                {% csrf_token %}
                <div class="textbox">
                    <input type="text" name="username" placeholder="Username" required>
                </div>
                <div class="textbox">
                    <input type="password" name="password" placeholder="Password" required>
                </div>
                <button type="submit" class="btn">Login</button>
                
            </form>
        </div>
    </div>
</body>
</html>


===== .:\Users\azizj\OneDrive\Documents\Aziz\GS\gs_project\gs_app\templates\suppliers\add_supplier.html =====
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Ajouter un Fournisseur</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css">
    <style>
        /* Base Layout */
        html, body {
            height: 100%;
            margin: 0;
        }

        body {
            display: flex;
            flex-direction: column;
            min-height: 100vh;
            background-color: #f8f9fa;
            color: #212529;
            transition: all 0.3s ease;
        }

        .main-container {
            flex: 1 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            margin: 20px auto;
            max-width: 600px;
        }

        footer {
            flex-shrink: 0;
            background-color: #f8f9fa;
            color: #212529;
            padding: 1rem 0;
            text-align: center;
        }

        /* Navbar */
        .navbar {
            background-color: #343a40;
            padding: 0.5rem 1rem;
        }

        .navbar-brand, .nav-link {
            color: white !important;
        }

        .nav-link:hover {
            color: #7ab8ff !important;
        }

        /* Dark Mode Styles */
        body.dark-mode {
            background-color: #121212;
            color: #e0e0e0;
        }

        .dark-mode .main-container {
            background-color: #1e1e1e;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
        }

        .dark-mode .navbar {
            background-color: #1e1e1e !important;
        }

        .dark-mode footer {
            background-color: #1e1e1e;
            color: #e0e0e0;
        }

        .dark-mode h2,
        .dark-mode .form-label,
        .dark-mode .form-text,
        .dark-mode label,
        .dark-mode p {
            color: #e0e0e0 !important;
        }

        .dark-mode .form-control,
        .dark-mode .form-select,
        .dark-mode input,
        .dark-mode textarea,
        .dark-mode select {
            background-color: #2d2d2d !important;
            color: #e0e0e0 !important;
            border-color: #444 !important;
        }

        .dark-mode .form-control:focus,
        .dark-mode .form-select:focus {
            background-color: #3d3d3d !important;
            border-color: #007bff !important;
            color: #e0e0e0 !important;
        }

        .dark-mode .btn-primary {
            background-color: #007bff;
            border-color: #007bff;
        }

        .dark-mode .btn-primary:hover {
            background-color: #0056b3;
            border-color: #004085;
        }

        .dark-mode a {
            color: #7ab8ff;
        }

        .dark-mode .card {
            background-color: #2d2d2d !important;
            border-color: #444 !important;
        }

        /* Dark Mode Toggle */
        .dark-mode-toggle-container {
            display: flex;
            align-items: center;
            margin-left: 15px;
        }

        .dark-mode-toggle {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 24px;
        }

        .dark-mode-toggle input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: 0.4s;
            border-radius: 24px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 16px;
            width: 16px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: 0.4s;
            border-radius: 50%;
        }

        .dark-mode-toggle input:checked + .slider {
            background-color: #007bff;
        }

        .dark-mode-toggle input:checked + .slider:before {
            transform: translateX(26px);
        }

        .dark-mode-toggle-label {
            margin-left: 10px;
            color: white;
        }

        .dark-mode .dark-mode-toggle-label {
            color: #e0e0e0;
        }

        /* General Styling */
        h2 {
            color: #343a40;
            margin-bottom: 20px;
        }

        .card {
            transition: background-color 0.3s ease;
        }

        .btn-block {
            width: 100%;
        }
    </style>
</head>
<body>
    <!-- Navigation Bar -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="{% url 'home' %}">Home</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'order_list' %}">Commandes</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'product_list' %}">Produits</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'supplier_list' %}">Fournisseurs</a>
                    </li>
                </ul>
                <div class="dark-mode-toggle-container">
                    <label class="dark-mode-toggle">
                        <input type="checkbox" id="toggleSwitch">
                        <span class="slider"></span>
                    </label>
                    <span class="dark-mode-toggle-label">Mode Sombre</span>
                </div>
            </div>
        </div>
    </nav>

    <!-- Add Supplier Form -->
    <div class="container main-container">
        <div class="card">
            <div class="card-header">
                <h2>Ajouter un Fournisseur</h2>
            </div>
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}
                    {{ form.as_p }}
                    <button type="submit" class="btn btn-primary btn-block">Ajouter</button>
                </form>
                <a href="{% url 'supplier_list' %}" class="btn btn-link mt-3">Retour à la liste des fournisseurs</a>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer>
        <p>© 2025 Gestion des Commandes - Tous droits réservés.</p>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Dark Mode Toggle
        const html = document.documentElement;
        const toggleSwitch = document.getElementById('toggleSwitch');

        // Load dark mode preference
        if (localStorage.getItem('darkMode') === 'true') {
            html.classList.add('dark-mode');
            toggleSwitch.checked = true;
        }

        // Toggle dark mode
        toggleSwitch.addEventListener('change', () => {
            html.classList.toggle('dark-mode');
            localStorage.setItem('darkMode', html.classList.contains('dark-mode'));
        });
    </script>
</body>
</html>

===== .:\Users\azizj\OneDrive\Documents\Aziz\GS\gs_project\gs_app\templates\suppliers\delete_supplier.html =====
<h2>Êtes-vous sûr de vouloir supprimer ce fournisseur ?</h2>

<form method="POST">
    {% csrf_token %}
    <button type="submit">Oui, supprimer</button>
    <a href="{% url 'supplier_list' %}"><button type="button">Annuler</button></a>
</form>

<p>Nom du fournisseur: {{ supplier.name }}</p>
<p>Contact: {{ supplier.contact }}</p>

===== .:\Users\azizj\OneDrive\Documents\Aziz\GS\gs_project\gs_app\templates\suppliers\edit_supplier.html =====
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Modifier un Fournisseur</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css">
    <style>
        /* Base Layout */
        html, body {
            height: 100%;
            margin: 0;
        }

        body {
            display: flex;
            flex-direction: column;
            min-height: 100vh;
            background-color: #f8f9fa;
            color: #212529;
            transition: all 0.3s ease;
        }

        .main-container {
            flex: 1 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            margin: 20px auto;
            max-width: 600px;
        }

        footer {
            flex-shrink: 0;
            background-color: #f8f9fa;
            color: #212529;
            padding: 1rem 0;
            text-align: center;
        }

        /* Navbar */
        .navbar {
            background-color: #343a40;
            padding: 0.5rem 1rem;
        }

        .navbar-brand, .nav-link {
            color: white !important;
        }

        .nav-link:hover {
            color: #7ab8ff !important;
        }

        /* Dark Mode Styles */
        body.dark-mode {
            background-color: #121212;
            color: #e0e0e0;
        }

        .dark-mode .main-container {
            background-color: #1e1e1e;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
        }

        .dark-mode .navbar {
            background-color: #1e1e1e !important;
        }

        .dark-mode footer {
            background-color: #1e1e1e;
            color: #e0e0e0;
        }

        .dark-mode h2,
        .dark-mode .form-label,
        .dark-mode .form-text,
        .dark-mode label,
        .dark-mode p {
            color: #e0e0e0 !important;
        }

        .dark-mode .form-control,
        .dark-mode .form-select,
        .dark-mode input,
        .dark-mode textarea,
        .dark-mode select {
            background-color: #2d2d2d !important;
            color: #e0e0e0 !important;
            border-color: #444 !important;
        }

        .dark-mode .form-control:focus,
        .dark-mode .form-select:focus {
            background-color: #3d3d3d !important;
            border-color: #007bff !important;
            color: #e0e0e0 !important;
        }

        .dark-mode .btn-primary {
            background-color: #007bff;
            border-color: #007bff;
        }

        .dark-mode .btn-primary:hover {
            background-color: #0056b3;
            border-color: #004085;
        }

        .dark-mode a {
            color: #7ab8ff;
        }

        .dark-mode .card {
            background-color: #2d2d2d !important;
            border-color: #444 !important;
        }

        /* Dark Mode Toggle */
        .dark-mode-toggle-container {
            display: flex;
            align-items: center;
            margin-left: 15px;
        }

        .dark-mode-toggle {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 24px;
        }

        .dark-mode-toggle input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: 0.4s;
            border-radius: 24px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 16px;
            width: 16px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: 0.4s;
            border-radius: 50%;
        }

        .dark-mode-toggle input:checked + .slider {
            background-color: #007bff;
        }

        .dark-mode-toggle input:checked + .slider:before {
            transform: translateX(26px);
        }

        .dark-mode-toggle-label {
            margin-left: 10px;
            color: white;
        }

        .dark-mode .dark-mode-toggle-label {
            color: #e0e0e0;
        }

        /* General Styling */
        h2 {
            color: #343a40;
            margin-bottom: 20px;
        }

        .card {
            transition: background-color 0.3s ease;
        }

        .btn-block {
            width: 100%;
        }
    </style>
</head>
<body>
    <!-- Navigation Bar -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="{% url 'home' %}">Home</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'order_list' %}">Commandes</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'product_list' %}">Produits</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'supplier_list' %}">Fournisseurs</a>
                    </li>
                </ul>
                <div class="dark-mode-toggle-container">
                    <label class="dark-mode-toggle">
                        <input type="checkbox" id="toggleSwitch">
                        <span class="slider"></span>
                    </label>
                    <span class="dark-mode-toggle-label">Mode Sombre</span>
                </div>
            </div>
        </div>
    </nav>

    <!-- Edit Supplier Form -->
    <div class="container main-container">
        <div class="card">
            <div class="card-header">
                <h2>Modifier un Fournisseur</h2>
            </div>
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}
                    {{ form.as_p }}
                    <button type="submit" class="btn btn-primary btn-block">Modifier</button>
                </form>
                <a href="{% url 'supplier_list' %}" class="btn btn-link mt-3">Retour à la liste des fournisseurs</a>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer>
        <p>© 2025 Gestion des Commandes - Tous droits réservés.</p>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Dark Mode Toggle
        const html = document.documentElement;
        const toggleSwitch = document.getElementById('toggleSwitch');

        // Load dark mode preference
        if (localStorage.getItem('darkMode') === 'true') {
            html.classList.add('dark-mode');
            toggleSwitch.checked = true;
        }

        // Toggle dark mode
        toggleSwitch.addEventListener('change', () => {
            html.classList.toggle('dark-mode');
            localStorage.setItem('darkMode', html.classList.contains('dark-mode'));
        });
    </script>
</body>
</html>

===== .:\Users\azizj\OneDrive\Documents\Aziz\GS\gs_project\gs_app\templates\suppliers\supplier_list.html =====
{% load static %}
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Liste des Fournisseurs</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css">
    <link href="{% static 'css/suppliers.css' %}" rel="stylesheet">
</head>
<body>
    <!-- Navigation Bar -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand d-flex align-items-center" href="{% url 'home' %}">
                <span class="fw-bold text-primary">SNDP</span>
                <span class="ms-1">Agil</span>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav mx-auto">
                    <li class="nav-item mx-2">
                        <a class="nav-link" href="{% url 'product_list' %}">
                            <i class="bi bi-fuel-pump"></i> Produits
                        </a>
                    </li>
                    <li class="nav-item mx-2">
                        <a class="nav-link" href="{% url 'order_list' %}">
                            <i class="bi bi-cart3"></i> Commandes
                        </a>
                    </li>
                    <li class="nav-item mx-2">
                        <a class="nav-link active" href="{% url 'supplier_list' %}">
                            <i class="bi bi-building"></i> Fournisseurs
                        </a>
                    </li>
                </ul>
                <div class="dark-mode-toggle-container">
                    <label class="dark-mode-toggle">
                        <input type="checkbox" id="darkModeToggle">
                        <span class="slider"></span>
                    </label>
                    <span class="dark-mode-toggle-label">Mode Sombre</span>
                </div>
            </div>
        </div>
    </nav>

    <!-- Header Section -->
    <div class="header-section">
        <div class="container">
            <div class="header-content text-center">
                <h1>
                    <i class="bi bi-building me-2"></i>
                    Gestion des Fournisseurs
                </h1>
                <p>Gérez vos partenaires fournisseurs</p>
            </div>
        </div>
    </div>

    <!-- Supplier List -->
    <div class="container">
        <div class="supplier-list shadow mb-4">
            <div class="card-header bg-transparent border-0 d-flex justify-content-between align-items-center">
                <h2><i class="bi bi-building me-2"></i>Liste des Fournisseurs</h2>
                <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#addSupplierModal">
                    <i class="bi bi-plus-circle me-1"></i> Ajouter un fournisseur
                </button>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th>ID</th>
                                <th>Nom</th>
                                <th>Contact</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for supplier in suppliers %}
                                <tr>
                                    <td>{{ supplier.id }}</td>
                                    <td>{{ supplier.name }}</td>
                                    <td>{{ supplier.contact }}</td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#editSupplierModal{{ supplier.id }}">
                                                <i class="bi bi-pencil"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#deleteSupplierModal{{ supplier.id }}">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            {% empty %}
                                <tr>
                                    <td colspan="4" class="text-center py-3">Aucun fournisseur trouvé</td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Supplier Modal -->
    <div class="modal fade" id="addSupplierModal" tabindex="-1" aria-labelledby="addSupplierModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addSupplierModalLabel">Ajouter un Fournisseur</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="addSupplierForm" method="post" action="{% url 'add_supplier' %}">
                        {% csrf_token %}
                        <div class="mb-3">
                            <label for="id_name" class="form-label">Nom</label>
                            <input type="text" class="form-control" id="id_name" name="name" required>
                        </div>
                        <div class="mb-3">
                            <label for="id_contact" class="form-label">Contact (8 chiffres)</label>
                            <input type="text" class="form-control" id="id_contact" name="contact" required pattern="[0-9]{8}" title="Le numéro doit contenir exactement 8 chiffres">
                            <div class="form-text">Entrez un numéro de téléphone à 8 chiffres</div>
                        </div>
                        <div class="text-end">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                            <button type="submit" class="btn btn-primary">Ajouter</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Supplier Modals -->
    {% for supplier in suppliers %}
    <div class="modal fade" id="editSupplierModal{{ supplier.id }}" tabindex="-1" aria-labelledby="editSupplierModalLabel{{ supplier.id }}" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editSupplierModalLabel{{ supplier.id }}">Modifier le Fournisseur</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="editSupplierForm{{ supplier.id }}" method="post" action="{% url 'edit_supplier' supplier.id %}">
                        {% csrf_token %}
                        <div class="mb-3">
                            <label for="id_name{{ supplier.id }}" class="form-label">Nom</label>
                            <input type="text" class="form-control" id="id_name{{ supplier.id }}" name="name" value="{{ supplier.name }}" required>
                        </div>
                        <div class="mb-3">
                            <label for="id_contact{{ supplier.id }}" class="form-label">Contact (8 chiffres)</label>
                            <input type="text" class="form-control" id="id_contact{{ supplier.id }}" name="contact" value="{{ supplier.contact }}" required pattern="[0-9]{8}" title="Le numéro doit contenir exactement 8 chiffres">
                            <div class="form-text">Entrez un numéro de téléphone à 8 chiffres</div>
                        </div>
                        <div class="text-end">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                            <button type="submit" class="btn btn-primary">Enregistrer</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    {% endfor %}

    <!-- Delete Supplier Modals -->
    {% for supplier in suppliers %}
    <div class="modal fade" id="deleteSupplierModal{{ supplier.id }}" tabindex="-1" aria-labelledby="deleteSupplierModalLabel{{ supplier.id }}" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="deleteSupplierModalLabel{{ supplier.id }}">Confirmer la suppression</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>Êtes-vous sûr de vouloir supprimer le fournisseur <strong>{{ supplier.name }}</strong> ?</p>
                    <p class="text-danger"><i class="bi bi-exclamation-triangle-fill"></i> Cette action est irréversible.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <form action="{% url 'delete_supplier' supplier.id %}" method="post" style="display:inline;">
                        {% csrf_token %}
                        <button type="submit" class="btn btn-danger">Supprimer</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
    {% endfor %}

    <!-- Footer -->
    <footer class="mt-5">
        <div class="container">
            <p class="text-center">© 2025 SNDP Agil - Tous droits réservés.</p>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Dark Mode Toggle
        const html = document.documentElement;
        const toggleSwitch = document.getElementById('darkModeToggle');

        // Load dark mode preference
        if (localStorage.getItem('darkMode') === 'true') {
            html.classList.add('dark-mode');
            toggleSwitch.checked = true;

            // Ensure table headers get proper styling in dark mode
            document.querySelectorAll('.table-light').forEach(el => {
                el.classList.add('table-dark');
                el.classList.remove('table-light');
            });
        }

        // Toggle dark mode
        toggleSwitch.addEventListener('change', () => {
            html.classList.toggle('dark-mode');
            localStorage.setItem('darkMode', html.classList.contains('dark-mode'));

            // Toggle table header styling based on dark mode
            const tableHeaders = document.querySelectorAll('thead');
            if (html.classList.contains('dark-mode')) {
                tableHeaders.forEach(header => {
                    if (header.classList.contains('table-light')) {
                        header.classList.add('table-dark');
                        header.classList.remove('table-light');
                    }
                });
            } else {
                tableHeaders.forEach(header => {
                    if (header.classList.contains('table-dark')) {
                        header.classList.add('table-light');
                        header.classList.remove('table-dark');
                    }
                });
            }
        });

        // Form validation for contact number
        document.addEventListener('DOMContentLoaded', function() {
            // Validate add form
            const addForm = document.getElementById('addSupplierForm');
            if (addForm) {
                addForm.addEventListener('submit', validateForm);
            }

            // Validate edit forms
            document.querySelectorAll('form[id^="editSupplierForm"]').forEach(form => {
                form.addEventListener('submit', validateForm);
            });

            function validateForm(e) {
                const contactInput = this.querySelector('input[name="contact"]');
                const contactValue = contactInput.value.trim();

                // Check if it's exactly 8 digits
                if (!/^\d{8}$/.test(contactValue)) {
                    e.preventDefault();
                    alert('Le numéro de contact doit contenir exactement 8 chiffres.');
                    contactInput.focus();
                    return false;
                }

                return true;
            }
        });
    </script>
</body>
</html>

