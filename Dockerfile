# Use a slim Python image for smaller size
FROM python:3.11-slim

# Set working directory
WORKDIR /app

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    libpq-dev \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy project files
COPY . .

# Collect static files (assuming STATIC_ROOT is set in settings.py)
RUN python manage.py collectstatic --noinput

# Expose port
EXPOSE 8000

# Run migrations and start the server
CMD ["sh", "-c", " python manage.py makemigrations && python manage.py migrate && python manage.py runserver 0.0.0.0:8000"]