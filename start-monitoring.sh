#!/bin/bash

echo "🚀 Starting SNDP Agile Application with Monitoring..."
echo ""

# Check if Dock<PERSON> is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker first."
    exit 1
fi

# Build and start all services
echo "📦 Building and starting all services..."
docker-compose up --build -d

echo ""
echo "⏳ Waiting for services to be ready..."

# Wait for Django app
echo "🔄 Waiting for Django application..."
timeout=60
counter=0
while [ $counter -lt $timeout ]; do
    if curl -s http://localhost:8000 > /dev/null 2>&1; then
        echo "✅ Django application is ready!"
        break
    fi
    echo "   Waiting... ($counter/$timeout seconds)"
    sleep 5
    counter=$((counter + 5))
done

# Wait for Prometheus
echo "🔄 Waiting for Prometheus..."
counter=0
while [ $counter -lt $timeout ]; do
    if curl -s http://localhost:9090/-/ready > /dev/null 2>&1; then
        echo "✅ Prometheus is ready!"
        break
    fi
    echo "   Waiting... ($counter/$timeout seconds)"
    sleep 5
    counter=$((counter + 5))
done

# Wait for Grafana
echo "🔄 Waiting for Grafana..."
counter=0
while [ $counter -lt $timeout ]; do
    if curl -s http://localhost:3000/api/health > /dev/null 2>&1; then
        echo "✅ Grafana is ready!"
        break
    fi
    echo "   Waiting... ($counter/$timeout seconds)"
    sleep 5
    counter=$((counter + 5))
done

echo ""
echo "🎉 All services are running!"
echo ""
echo "📊 Access your services:"
echo "   🌐 SNDP Agile App:    http://localhost:8000"
echo "   📈 Prometheus:        http://localhost:9090"
echo "   📊 Grafana:           http://localhost:3000 (aziz/aziz123)"
echo "   🔍 Django Metrics:    http://localhost:8000/metrics"
echo ""
echo "📋 Grafana Setup:"
echo "   1. Login to Grafana with aziz/aziz123"
echo "   2. Prometheus datasource is auto-configured"
echo "   3. Import the Django dashboard from the provisioning"
echo ""
echo "🛑 To stop all services: docker-compose down"
echo "📊 To view logs: docker-compose logs -f [service_name]"
