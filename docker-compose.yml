version: '3.8'

services:
  web:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "0.0.0.0:8000:8000"
    environment:
      - DATABASE_URL=**********************************/gs_mng_db
      - SECRET_KEY=django-insecure-vym%sk*1humbpjbh*_f=cb-+4@v59e+h7ahe^clcl3&tst3si+
      - DEBUG=True
      - EMAIL_HOST_USER=<EMAIL>
      - EMAIL_HOST_PASSWORD=phmcxtyakkmocbsq
      - OLLAMA_HOST=http://ollama:11434
    volumes:
      - .:/app
      - staticfiles:/app/staticfiles
      - media:/app/media
    depends_on:
      - db
      - ollama
    networks:
      - app-network

  db:
    image: postgres:15
    environment:
      - POSTGRES_DB=gs_mng_db
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=1234
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - app-network

  ollama:
    image: ollama/ollama:latest
    container_name: ollama
    ports:
      - "11434:11434"
    volumes:
      - ollama_data:/root/.ollama
    environment:
      - OLLAMA_ORIGINS=*
    networks:
      - app-network
    restart: unless-stopped
    # For GPU support (uncomment if you have NVIDIA GPU)
    # deploy:
    #   resources:
    #     reservations:
    #       devices:
    #         - driver: nvidia
    #           count: 1
    #           capabilities: [gpu]

  ollama-init:
    image: curlimages/curl:latest
    container_name: ollama-init
    depends_on:
      - ollama
    networks:
      - app-network
    volumes:
      - ./init-ollama.sh:/init-ollama.sh
    command: sh /init-ollama.sh
    restart: "no"

  prometheus:
    image: prom/prometheus:v2.54.1
    container_name: prometheus_server
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=15d'
      - '--web.enable-lifecycle'
    networks:
      - app-network
    restart: unless-stopped
    mem_limit: 1g
    cpus: "0.5"

  grafana:
    image: grafana/grafana:11.2.0
    container_name: grafana_server
    ports:
      - "0.0.0.0:3000:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
    environment:
      - GF_SECURITY_ADMIN_USER=aziz
      - GF_SECURITY_ADMIN_PASSWORD=aziz123
      - GF_INSTALL_PLUGINS=grafana-piechart-panel
    networks:
      - app-network
    restart: unless-stopped
    mem_limit: 512m
    cpus: "0.3"
    depends_on:
      - prometheus

volumes:
  postgres_data:
  staticfiles:
  media:
  ollama_data:
  prometheus_data:
  grafana_data:

networks:
  app-network:
    driver: bridge