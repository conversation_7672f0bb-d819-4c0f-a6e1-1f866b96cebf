#!/bin/bash

# Debug script for <PERSON> Docker Compose issues
# Run this script to diagnose Docker Compose problems

echo "🔍 Jenkins Docker Compose Troubleshooting"
echo "=========================================="

# Check if Jenkins container is running
echo ""
echo "1. 📊 Checking Jenkins container status..."
if docker ps | grep jenkins_server > /dev/null; then
    echo "✅ Jenkins container is running"
    docker ps --filter "name=jenkins_server" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
else
    echo "❌ Jenkins container is not running"
    echo "Starting Jenkins container..."
    docker-compose -f jenkins-docker-compose.yml up -d
fi

echo ""
echo "2. 🐳 Testing Docker access from Jenkins container..."
docker exec jenkins_server docker --version 2>/dev/null && echo "✅ Docker accessible" || echo "❌ Docker not accessible"

echo ""
echo "3. 🔧 Testing Docker Compose access from Jenkins container..."
if docker exec jenkins_server docker-compose --version 2>/dev/null; then
    echo "✅ docker-compose (V1) accessible"
elif docker exec jenkins_server docker compose version 2>/dev/null; then
    echo "✅ docker compose (V2) accessible"
else
    echo "❌ Docker Compose not accessible"
    echo "Installing Docker Compose in Jenkins container..."
    docker exec -u root jenkins_server bash -c "
        curl -L 'https://github.com/docker/compose/releases/download/v2.24.0/docker-compose-\$(uname -s)-\$(uname -m)' -o /usr/local/bin/docker-compose
        chmod +x /usr/local/bin/docker-compose
        ln -sf /usr/local/bin/docker-compose /usr/bin/docker-compose
    "
    docker exec jenkins_server docker-compose --version && echo "✅ Docker Compose installed" || echo "❌ Installation failed"
fi

echo ""
echo "4. 🔌 Testing Docker socket connection from Jenkins..."
docker exec jenkins_server docker info > /dev/null 2>&1 && echo "✅ Docker socket accessible" || echo "❌ Docker socket not accessible"

echo ""
echo "5. 📁 Testing host directory access from Jenkins..."
if docker exec jenkins_server ls -la /opt 2>/dev/null; then
    echo "✅ Host /opt directory accessible"
else
    echo "❌ Host /opt directory not accessible"
fi

echo ""
echo "6. 🏠 Testing deployment directory..."
if docker exec jenkins_server ls -la /opt/sndp-agile 2>/dev/null; then
    echo "✅ Deployment directory exists and accessible"
    echo "Contents:"
    docker exec jenkins_server ls -la /opt/sndp-agile
else
    echo "⚠️ Deployment directory doesn't exist or not accessible"
    echo "Creating deployment directory..."
    docker exec -u root jenkins_server mkdir -p /opt/sndp-agile
    docker exec -u root jenkins_server chown jenkins:jenkins /opt/sndp-agile
fi

echo ""
echo "7. 📋 Testing Docker Compose with your project..."
if [ -f "/opt/sndp-agile/docker-compose.yml" ]; then
    echo "Testing docker-compose config validation..."
    if docker exec jenkins_server bash -c "cd /opt/sndp-agile && docker-compose config" 2>/dev/null; then
        echo "✅ Docker Compose config is valid"
    else
        echo "❌ Docker Compose config validation failed"
        echo "Trying with explicit paths..."
        docker exec jenkins_server docker-compose -f /opt/sndp-agile/docker-compose.yml --project-directory /opt/sndp-agile config
    fi
else
    echo "⚠️ docker-compose.yml not found in deployment directory"
fi

echo ""
echo "8. 🔍 Checking Docker system info..."
echo "Docker version:"
docker exec jenkins_server docker --version
echo ""
echo "Docker system info:"
docker exec jenkins_server docker system df
echo ""
echo "Running containers:"
docker exec jenkins_server docker ps

echo ""
echo "9. 🛠️ Jenkins container details..."
echo "Jenkins container mounts:"
docker inspect jenkins_server | grep -A 20 '"Mounts"'

echo ""
echo "10. 📊 System resources..."
echo "Host disk space:"
df -h /opt
echo ""
echo "Host memory:"
free -h

echo ""
echo "🎯 Recommendations:"
echo "=================="

# Check Docker socket permissions
if [ ! -S /var/run/docker.sock ]; then
    echo "❌ Docker socket not found. Make sure Docker is running."
elif [ ! -r /var/run/docker.sock ]; then
    echo "❌ Docker socket not readable. Fix permissions:"
    echo "   sudo chmod 666 /var/run/docker.sock"
fi

# Check if docker-compose exists on host
if command -v docker-compose >/dev/null 2>&1; then
    echo "✅ docker-compose found on host: $(which docker-compose)"
    echo "   Consider mounting it: -v $(which docker-compose):/usr/local/bin/docker-compose:ro"
elif docker compose version >/dev/null 2>&1; then
    echo "✅ docker compose (V2) available on host"
    echo "   Jenkins will use 'docker compose' commands"
else
    echo "⚠️ No Docker Compose found on host"
fi

# Check deployment directory
if [ ! -d "/opt/sndp-agile" ]; then
    echo "⚠️ Create deployment directory: sudo mkdir -p /opt/sndp-agile"
fi

echo ""
echo "🔧 Quick fixes:"
echo "==============="
echo "1. Restart Jenkins with proper mounts:"
echo "   docker-compose -f jenkins-docker-compose.yml down"
echo "   docker-compose -f jenkins-docker-compose.yml up -d"
echo ""
echo "2. Fix Docker socket permissions:"
echo "   sudo chmod 666 /var/run/docker.sock"
echo ""
echo "3. Install Docker Compose in Jenkins:"
echo "   docker exec -u root jenkins_server bash -c 'curl -L \"https://github.com/docker/compose/releases/download/v2.24.0/docker-compose-\$(uname -s)-\$(uname -m)\" -o /usr/local/bin/docker-compose && chmod +x /usr/local/bin/docker-compose'"
echo ""
echo "4. Create deployment directory:"
echo "   sudo mkdir -p /opt/sndp-agile"
echo "   sudo chown 1000:1000 /opt/sndp-agile"

echo ""
echo "✅ Troubleshooting complete!"
