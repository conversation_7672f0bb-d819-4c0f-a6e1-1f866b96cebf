from django import forms
from django.core.validators import RegexValidator
from .models import Product, Supplier, Order, OrderDetail

# Formulaire pour Product
class ProductForm(forms.ModelForm):
    class Meta:
        model = Product
        fields = ['name', 'category', 'supplier', 'quantity_in_stock', 'price']

# Formulaire pour Supplier
class SupplierForm(forms.ModelForm):
    # Add a validator to ensure contact is exactly 8 digits
    contact = forms.CharField(
        validators=[
            RegexValidator(
                regex=r'^\d{8}$',
                message='Le numéro de contact doit contenir exactement 8 chiffres.',
                code='invalid_contact'
            )
        ],
        widget=forms.TextInput(attrs={'pattern': '[0-9]{8}', 'title': 'Le numéro doit contenir exactement 8 chiffres'})
    )
    
    class Meta:
        model = Supplier
        fields = ['name', 'contact']

class OrderForm(forms.ModelForm):
    products = forms.ModelMultipleChoiceField(
        queryset=Product.objects.all(),
        widget=forms.CheckboxSelectMultiple,
        required=True
    )
    quantities = forms.CharField(
        widget=forms.HiddenInput(),
        required=False
    )

    class Meta:
        model = Order
        fields = ['status', 'products', 'quantities']

class OrderDetailForm(forms.ModelForm):
    class Meta:
        model = OrderDetail
        fields = ['product', 'quantity', 'price_at_order']
