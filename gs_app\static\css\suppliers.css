/* Suppliers Page Professional Design with Black & Yellow Theme */
:root {
    --primary-color: #000000;
    --secondary-color: #ffff00;
    --accent-color: #ffd700;
    --dark-color: #1f1f1f;
    --light-color: #f8fafc;
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --gradient-primary: linear-gradient(135deg, #000000 0%, #333333 100%);
    --gradient-secondary: linear-gradient(135deg, #ffff00 0%, #ffd700 100%);
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON> UI', <PERSON><PERSON>, sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--light-color);
    padding: 20px;
    transition: all 0.3s ease;
}

/* Navigation */
.navbar {
    background: var(--gradient-primary) !important;
    backdrop-filter: blur(10px);
    box-shadow: var(--shadow-md);
    padding: 1rem;
    margin-bottom: 20px;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
    color: var(--secondary-color) !important;
    text-decoration: none;
}

.nav-link {
    color: rgba(255, 255, 255, 0.9) !important;
    font-weight: 500;
    padding: 0.5rem 1rem !important;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
    position: relative;
}

.nav-link:hover {
    color: var(--secondary-color) !important;
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateY(-1px);
}

.nav-link.active {
    background-color: rgba(255, 255, 0, 0.2) !important;
    color: var(--secondary-color) !important;
}

/* Header Section */
.header-section {
    background: var(--gradient-primary);
    color: white;
    padding: 4rem 0 3rem;
    margin-bottom: 2rem;
    border-radius: 1rem;
    position: relative;
    overflow: hidden;
}

.header-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 100" fill="rgba(255,255,0,0.05)"><polygon points="1000,100 1000,0 0,100"/></svg>');
    background-size: cover;
}

.header-content {
    position: relative;
    z-index: 2;
}

.header-section h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    background: linear-gradient(45deg, #ffffff, var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.header-section p {
    font-size: 1.1rem;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 0;
}

/* Main Content */
.supplier-list {
    background: white;
    border-radius: 1rem;
    box-shadow: var(--shadow-md);
    border: 1px solid rgba(0, 0, 0, 0.05);
    position: relative;
    overflow: hidden;
    margin-bottom: 1.5rem;
}

.supplier-list::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-secondary);
}

.card-header {
    background: transparent !important;
    border: none !important;
    padding: 2rem 2rem 1rem;
}

.card-header h2 {
    font-size: 1.75rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0;
}

.card-body {
    padding: 0 2rem 2rem;
}

/* Table Styling */
.table-responsive {
    border-radius: 0.75rem;
    overflow: hidden;
}

.table {
    margin-bottom: 0;
}

.table th {
    background-color: var(--light-color);
    border: none;
    font-weight: 600;
    color: var(--text-primary);
    padding: 1rem;
}

.table td {
    border: none;
    padding: 1rem;
    vertical-align: middle;
}

.table tbody tr {
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    transition: background-color 0.2s ease;
}

.table tbody tr:hover {
    background-color: rgba(255, 255, 0, 0.05);
}

/* Buttons */
.btn-primary {
    background: var(--gradient-primary);
    border: none;
    color: var(--secondary-color);
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
    color: var(--secondary-color);
}

.btn-success {
    background: var(--gradient-secondary);
    border: none;
    color: var(--primary-color);
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
}

.btn-success:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
    color: var(--primary-color);
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
}

.btn-outline-primary {
    border-color: var(--primary-color);
    color: var(--primary-color);
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--secondary-color);
    transform: translateY(-1px);
}

.btn-outline-danger {
    border-color: #dc3545;
    color: #dc3545;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-outline-danger:hover {
    background-color: #dc3545;
    border-color: #dc3545;
    color: white;
    transform: translateY(-1px);
}

/* Modals */
.modal-content {
    border-radius: 1rem;
    border: none;
    box-shadow: var(--shadow-xl);
}

.modal-header {
    background: var(--gradient-primary);
    color: white;
    border-radius: 1rem 1rem 0 0;
    border-bottom: none;
}

.modal-header .modal-title {
    font-weight: 600;
    color: var(--secondary-color);
}

.modal-header .btn-close {
    filter: brightness(0) invert(1);
}

.modal-body {
    padding: 2rem;
}

.modal-footer {
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    padding: 1.5rem 2rem;
}

/* Form Styling */
.form-label {
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
}

.form-control,
.form-select {
    padding: 0.75rem 1rem;
    font-size: 1rem;
    line-height: 1.5;
    color: var(--text-primary);
    background-color: #fff;
    border: 2px solid #e2e8f0;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
}

.form-control:focus,
.form-select:focus {
    border-color: var(--primary-color);
    outline: 0;
    box-shadow: 0 0 0 0.25rem rgba(0, 0, 0, 0.1);
}

.form-text {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin-top: 0.5rem;
}

/* Dark Mode Toggle */
.dark-mode-toggle-container {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.dark-mode-toggle {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.dark-mode-toggle input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: 0.4s;
    border-radius: 24px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: 0.4s;
    border-radius: 50%;
}

.dark-mode-toggle input:checked + .slider {
    background-color: var(--secondary-color);
}

.dark-mode-toggle input:checked + .slider:before {
    transform: translateX(26px);
}

.dark-mode-toggle-label {
    color: white;
    font-weight: 500;
}

/* Footer */
footer {
    background: var(--gradient-primary);
    color: white;
    padding: 2rem 0;
    margin-top: 3rem;
    border-radius: 1rem;
    text-align: center;
}

footer p {
    margin: 0;
    color: rgba(255, 255, 255, 0.8);
}

/* Dark Mode Styles */
html.dark-mode body {
    background-color: #121212;
    color: #e0e0e0;
}

html.dark-mode .supplier-list {
    background-color: #1e1e1e !important;
    border-color: #444;
    color: #e0e0e0;
}

html.dark-mode .card {
    background-color: #1e1e1e !important;
    color: #e0e0e0;
}

html.dark-mode .card-body {
    background-color: #1e1e1e !important;
}

html.dark-mode .card-header h2 {
    color: #e0e0e0;
}

html.dark-mode .table th {
    background-color: #2d2d2d;
    color: #e0e0e0;
}

html.dark-mode .table tbody tr {
    border-bottom-color: #333;
}

html.dark-mode .table tbody tr:hover {
    background-color: rgba(255, 255, 0, 0.1);
}

html.dark-mode .table {
    color: #e0e0e0;
    background-color: transparent;
}

html.dark-mode .table-responsive {
    background-color: #1e1e1e !important;
}

html.dark-mode .table tbody tr {
    background-color: transparent;
}

html.dark-mode .table td {
    background-color: transparent !important;
    color: #e0e0e0 !important;
}

html.dark-mode .table tbody {
    background-color: transparent !important;
}

html.dark-mode .table thead th {
    background-color: #2d2d2d !important;
    color: #e0e0e0 !important;
}

html.dark-mode .modal-content {
    background-color: #1e1e1e;
    color: #e0e0e0;
}

html.dark-mode .form-control,
html.dark-mode .form-select {
    background-color: #2c2c2c;
    border-color: #444;
    color: #e0e0e0;
}

html.dark-mode .form-control:focus,
html.dark-mode .form-select:focus {
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 0.25rem rgba(255, 255, 0, 0.25);
}

html.dark-mode .form-label {
    color: #e0e0e0;
}

html.dark-mode .form-text {
    color: #adb5bd;
}

html.dark-mode .dark-mode-toggle-label {
    color: #e0e0e0;
}

html.dark-mode .btn-outline-primary {
    border-color: var(--secondary-color);
    color: var(--secondary-color);
}

html.dark-mode .btn-outline-primary:hover {
    background-color: var(--secondary-color);
    color: var(--primary-color);
}

html.dark-mode .btn-outline-danger {
    border-color: #ff6666;
    color: #ff6666;
}

html.dark-mode .btn-outline-danger:hover {
    background-color: #dc3545;
    color: white;
}

html.dark-mode .text-danger {
    color: #ff6666 !important;
}
