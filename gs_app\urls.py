from gs_app.views import upload_excel
from django.urls import path
from . import views






urlpatterns = [
    path('', views.home, name='home'),  # Page d'accueil
    path('products/', views.product_list, name='product_list'),  # Liste des produits
    path('products/add/', views.add_product, name='add_product'),  # Ajout d'un produit
    path('products/edit/<int:product_id>/', views.edit_product, name='edit_product'),  # Modification
    path('products/delete/<int:product_id>/', views.delete_product, name='delete_product'),  # Suppression
    path('suppliers/', views.supplier_list, name='supplier_list'),  # Liste des fournisseurs
    path('suppliers/add/', views.add_supplier, name='add_supplier'),  # Ajouter un fournisseur
    path('suppliers/<int:id>/', views.supplier_detail, name='supplier_detail'),  # Détails d'un fournisseur
    path('suppliers/<int:id>/edit/', views.edit_supplier, name='edit_supplier'),  # Modifier un fournisseur
    path('suppliers/<int:id>/delete/', views.delete_supplier, name='delete_supplier'),  # Supprimer un fournisseur
    path('orders/', views.order_list, name='order_list'),  # Liste des commandes
    path('orders/add/', views.add_order, name='add_order'),  # Ajouter une commande
    path('orders/edit/<int:order_id>/', views.edit_order, name='edit_order'),  # Modifier une commande
    path('orders/delete/<int:order_id>/', views.delete_order, name='delete_order'),  # Supprimer une commande
    path('orders/<int:order_id>/details/', views.order_detail_list, name='order_detail_list'),  # Détails d'une commande
    path('orders/<int:order_id>/details/add/', views.add_order_detail, name='add_order_detail'),  # Ajouter un détail
    path('orders/details/edit/<int:order_detail_id>/', views.edit_order_detail, name='edit_order_detail'),  # Modifier un détail
    path('orders/details/delete/<int:order_detail_id>/', views.delete_order_detail, name='delete_order_detail'),  # Supprimer un détail
    path('ollama-chat/', views.ollama_chat, name='ollama_chat'),
    path('upload-stock/', upload_excel, name='upload_excel'),
    path('dashboard/', views.dashboard, name='dashboard'),
    path('map/', views.map_view, name='map'),
    path('translate/', views.translate_text, name='translate'),
    
    
]
